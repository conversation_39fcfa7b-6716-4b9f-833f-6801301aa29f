import asyncio
from dataclasses import asdict, dataclass, field, fields
import datetime
import json
import math
import os
import socket
import sys
import threading
import time
import tkinter as tk
import tkinter.messagebox as messagebox
import yaml
import aiohttp
import pyautogui
from pynput import keyboard
import regex
from typing import Optional, Dict, List, Callable, Any, Tuple, Union
import logging
import pycld2 as cld2
import collections
import pyperclip
import tkinter.ttk as ttk
from difflib import SequenceMatcher
from api_crypto import ApiCrypto  # 导入外部加密模块
import queue
import ctypes  # 修复ctypes未定义
import gc
import io
from ruamel.yaml import YAML
from ruamel.yaml.scalarstring import PreservedScalarString
from dataclasses import asdict, is_dataclass, fields
from typing import Dict, Any, List, Optional, Union, Callable # 确保导入 Union
import platform
import re
import langid
import unicodedata
from collections import defaultdict
import copy

# 全局变量
api_crypto = None  # ApiCrypto实例
language_detection_cache = None

# 添加LRU缓存类用于语言检测和翻译缓存
class LRUCache:
    """简单的LRU缓存实现"""
    
    def __init__(self, capacity: int):
        """初始化LRU缓存
        
        Args:
            capacity: 缓存容量上限
        """
        self.cache = collections.OrderedDict()
        self.capacity = capacity
        self._lock = threading.Lock()  # 添加线程锁确保线程安全
        # 添加统计信息
        self.hits = 0
        self.misses = 0
        logger.debug(f"创建LRU缓存，容量: {capacity}")
    
    def get(self, key: str) -> Any:
        """获取缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值，如果不存在则返回None
        """
        with self._lock:
            if key not in self.cache:
                self.misses += 1
                return None
            # 移动到末尾表示最近使用
            self.cache.move_to_end(key)
            self.hits += 1
            # 记录缓存命中信息
            logger.info(f"【缓存命中】键长度: {len(key)}, 值类型: {type(self.cache[key]).__name__}")
            # 记录缓存命中率
            if (self.hits + self.misses) % 10 == 0:  # 每10次请求记录一次
                hit_rate = self.hits / (self.hits + self.misses) * 100
                logger.debug(f"LRU缓存命中率: {hit_rate:.1f}% (命中: {self.hits}, 未命中: {self.misses})")
            return self.cache[key]
    
    def put(self, key: str, value: Any) -> None:
        """添加或更新缓存项
        
        Args:
            key: 缓存键
            value: 要缓存的值
        """
        with self._lock:
            if key in self.cache:
                # 已存在则移到末尾
                self.cache.move_to_end(key)
            elif len(self.cache) >= self.capacity:
                # 缓存已满，移除最久未使用的项
                removed_key, _ = self.cache.popitem(last=False)
                logger.debug(f"缓存已满，移除最久未使用项: {removed_key[:20]}...")
            self.cache[key] = value

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self.cache.clear()
        
    def __len__(self) -> int:
        """返回当前缓存项数量"""
        with self._lock:
            return len(self.cache)

def init_api_crypto():
    """初始化API密钥加密工具"""
    global api_crypto
    if api_crypto is None:
        try:
            api_crypto = ApiCrypto()  # 使用外部ApiCrypto类
            logger.debug("API加密工具已初始化")
        except Exception as e:
            logger.error(f"初始化API加密工具失败: {e}")
    return api_crypto

def get_real_api_key(api_key):
    """获取真实（解密后）的API密钥
    
    Args:
        api_key: 配置中的API密钥（可能是加密的）
        
    Returns:
        str: 解密后的API密钥
    """
    global api_crypto
    
    # 如果API密钥为空，返回空字符串
    if not api_key:
        return ""
    
    # 初始化API加密工具
    if not api_crypto:
        init_api_crypto()
        if not api_crypto:
            logger.error("API加密工具初始化失败，无法解密API密钥")
            return api_key  # 如果初始化失败，返回原始密钥
    
    # 检查API密钥是否已加密
    try:
        if api_crypto.is_encrypted(api_key):
            # 解密API密钥
            decrypted = api_crypto.decrypt(api_key)
            if decrypted:
                # logger.debug("API密钥已成功解密") # 已移除
                return decrypted
            else:
                logger.error("API密钥解密失败") # 保留解密失败的日志
                return ""
        else:
            # 如果不是加密的API密钥，直接返回
            # logger.debug("API密钥未加密，将直接使用") # 已移除
            return api_key
    except Exception as e:
        logger.error(f"处理API密钥时出错: {e}")
        return api_key  # 出错时返回原始密钥

# 设置控制台标准输出编码为 UTF-8
from typing import cast
from io import TextIOWrapper

if sys.stdout.encoding is None or sys.stdout.encoding.lower() != 'utf-8':
    try:
        # Python 3.7+ 方法
        if isinstance(sys.stdout, TextIOWrapper) and hasattr(sys, 'reconfigure'):
            # 使用具体类型替代泛型IO
            stdout = cast(TextIOWrapper, sys.stdout)
            if hasattr(stdout, 'reconfigure'):
                stdout.reconfigure(encoding='utf-8')
        else:
            raise AttributeError("reconfigure method not available")
    except AttributeError:
        # 兼容旧版本Python
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 日志配置
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Configure logging
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 文件日志处理器（修复file_handler未定义）
try:
    from logging.handlers import RotatingFileHandler
    log_file = os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), "app.log")
    file_handler = RotatingFileHandler(log_file, maxBytes=2*1024*1024, backupCount=3, encoding="utf-8")
    file_handler.setFormatter(formatter) # 添加formatter
    file_handler.setLevel(logging.INFO)    # 设置日志级别
    logger.addHandler(file_handler)      # 添加到logger
except Exception as e:
    file_handler = None # 确保在出错时 file_handler 被定义
    logger.warning(f"文件日志处理器初始化失败: {e}")

# 配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), "config.yaml")
MODE_CONFIG_FILE = os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), "mode_config.yaml")

# 通用提示词模板
UNIVERSAL_PROMPT_TEMPLATE = """
你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 {input_lang} 和输出为 {output_lang} ，将内容从 {input_lang} 翻译成 {output_lang} {style_instruction}
- 若未指定语言或输入既非 {source_lang} 也非 {target_lang} ，则翻译为 {default_lang} 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 {output_lang}，调整语气和风格，考虑文化内涵和地区差异。不得包含 {input_lang} 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 {source_tone} 中的语气助词时，才将其翻译为 {output_lang} 中等效的 {target_tone} 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 {output_lang} 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 {output_lang} 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！
"""

def load_config() -> Dict:
    """加载配置文件，如果文件不存在或无效，则自动生成默认配置。"""
    # 先声明全局变量
    global CONFIG_FILE
    
    try:
        # 导入配置管理模块
        from config_management import get_default_config_dict, save_main_config, get_application_path, is_path_writable
    except ImportError:
        logger.error("无法导入config_management模块，请确保它存在于同一目录")
        sys.exit(1)
        
    # 检查主配置文件路径是否可写    
    if not is_path_writable(CONFIG_FILE):
        logger.warning(f"无法写入主配置文件路径 {CONFIG_FILE}，将尝试使用备用路径。")
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        try:
            os.makedirs(app_data_dir, exist_ok=True)
            backup_config_file = os.path.join(app_data_dir, os.path.basename(CONFIG_FILE))
            logger.info(f"将使用备用配置文件路径: {backup_config_file}")
            # 如果备用位置已有文件，我们将使用该文件
            if os.path.exists(backup_config_file):
                try:
                    with open(backup_config_file, "r", encoding="utf-8-sig") as f:
                        yaml_loader = YAML()
                        config = yaml_loader.load(f)
                    if isinstance(config, dict):
                        logger.info(f"已从备用路径 {backup_config_file} 加载配置")
                        # 将CONFIG_FILE设置为备用路径，以便后续操作使用正确的文件
                        CONFIG_FILE = backup_config_file
                except Exception as e:
                    logger.error(f"从备用路径加载配置失败: {e}")
            else:
                # 如果备用位置没有文件，全局修改CONFIG_FILE指向备用位置
                CONFIG_FILE = backup_config_file
                logger.info(f"已将配置文件路径修改为备用路径: {CONFIG_FILE}")
        except Exception as e:
            logger.error(f"创建备用配置目录失败: {e}")
    
    if not os.path.exists(CONFIG_FILE):
        logger.warning(f"主配置文件 {CONFIG_FILE} 不存在，正在自动生成默认配置...")
        try:
            # 导入配置管理模块
            from config_management import generate_default_main_config
            # 生成默认配置
            if generate_default_main_config(force_overwrite=True):
                logger.info(f"已成功生成默认配置文件: {CONFIG_FILE}")
            else:
                logger.error(f"生成默认配置文件失败，尝试加载备用配置")
                # 尝试加载备用配置
                user_home = os.path.expanduser("~")
                app_data_dir = os.path.join(user_home, ".multitranslator")
                backup_config_file = os.path.join(app_data_dir, os.path.basename(CONFIG_FILE))
                if os.path.exists(backup_config_file):
                    try:
                        with open(backup_config_file, "r", encoding="utf-8-sig") as f:
                            yaml_loader = YAML()
                            config_data = yaml_loader.load(f)
                        if isinstance(config_data, dict):
                            logger.info(f"已从备用路径 {backup_config_file} 加载配置")
                            return config_data
                    except Exception as e_backup:
                        logger.error(f"从备用路径加载配置失败: {e_backup}")
                
                # 如果无法从任何位置加载，使用内存中的默认配置
                logger.warning("将使用内存中的默认配置")
                return get_default_config_dict()
        except Exception as e:
            logger.error(f"生成默认配置文件时出错: {e}")
            logger.warning("将使用内存中的默认配置")
            return get_default_config_dict()
    
    try:
        with open(CONFIG_FILE, "r", encoding="utf-8-sig") as f:
            yaml_loader = YAML()
            config_data = yaml_loader.load(f)
            
        if not isinstance(config_data, dict):
            logger.error(f"主配置文件 {CONFIG_FILE} 内容格式不正确")
            logger.warning("将使用默认配置")
            return get_default_config_dict()
        
        # 深层合并确保默认值存在
        default_config = get_default_config_dict()
        from config_management import deep_merge_configs
        config_data = deep_merge_configs(default_config.copy(), config_data)
        
        # 兼容旧的 chain_of_thought_enabled 配置
        if 'chain_of_thought_enabled' in config_data:
            logger.info("检测到旧的 'chain_of_thought_enabled' 配置项，将自动转换为 'thinking_budget_tokens'。")
            if config_data['chain_of_thought_enabled']:
                # 从 default_config 获取默认的 thinking_budget_tokens 值
                default_thinking_budget = default_config.get('thinking_budget_tokens', 256)
                config_data['thinking_budget_tokens'] = default_thinking_budget
                logger.info(f"旧配置 'chain_of_thought_enabled: True' 已转换为 'thinking_budget_tokens: {default_thinking_budget}'。")
            else:
                config_data['thinking_budget_tokens'] = 0
                logger.info("旧配置 'chain_of_thought_enabled: False' 已转换为 'thinking_budget_tokens: 0'。")
            del config_data['chain_of_thought_enabled']
            # 提示用户新的配置已生效，并建议检查配置文件
            logger.info("旧配置已成功转换。如果您想调整思考预算，请在设置菜单中修改 'thinking_budget_tokens' 或直接编辑 config.yaml。")
            # 可以在这里选择是否自动保存更新后的配置到 config.yaml
            # from config_management import save_main_config # 确保导入
            # if save_main_config(config_data, CONFIG_FILE):
            #    logger.info(f"转换后的配置已自动保存到 {CONFIG_FILE}")
            # else:
            #    logger.error(f"自动保存转换后的配置到 {CONFIG_FILE} 失败。")

        return config_data
    except Exception as e:
        logger.error(f"加载主配置文件 {CONFIG_FILE} 失败: {e}")
        logger.error(f"请检查文件权限和内容，或通过 'python config_management.py --generate-config --force' 重新生成。")
        # 尝试从备用位置加载
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        backup_config_file = os.path.join(app_data_dir, os.path.basename(CONFIG_FILE))
        if os.path.exists(backup_config_file):
            try:
                with open(backup_config_file, "r", encoding="utf-8-sig") as f:
                    yaml_loader = YAML()
                    config_data = yaml_loader.load(f)
                if isinstance(config_data, dict):
                    logger.info(f"已从备用路径 {backup_config_file} 加载配置")
                    return config_data
            except Exception as e_backup:
                logger.error(f"从备用路径加载配置失败: {e_backup}")
        
        # 如果从备用位置也无法加载，使用内存中的默认配置
        logger.warning("将使用内存中的默认配置")
        return get_default_config_dict()

def prompt_for_api_key() -> str:
    """提示用户输入API密钥"""
    logger.info("未在 config.yaml 中找到有效 API 密钥，请输入 API 密钥。")
    logger.info("对于Google Gemini API，请访问 https://ai.google.dev/ 获取API密钥")
    logger.info("对于OpenAI API，请访问 https://platform.openai.com/account/api-keys 获取API密钥")
    logger.info("请使用api_crypto.py工具加密您的API密钥以确保安全")
    
    while True:
        api_key = input("请输入API密钥（加密或明文，输入后按回车）：").strip()
        if not api_key:
            logger.warning("API 密钥不能为空，请重新输入。")
            continue
            
        # 初始化API加密工具
        init_api_crypto()
        if not api_crypto:
            logger.error("API加密工具初始化失败，无法验证API密钥")
            if input("是否继续使用未验证的API密钥？(y/n): ").lower() == 'y':
                return api_key
            continue
            
        # 检查是否为加密的API密钥
        if api_crypto.is_encrypted(api_key):
            try:
                # 尝试解密以验证有效性
                decrypted = api_crypto.decrypt(api_key)
                if decrypted:
                    logger.info("API密钥验证成功（已加密）")
                    # 是否需要保存原始的加密密钥
                    if input("是否将此加密API密钥保存到配置文件？(y/n): ").lower() == 'y':
                        return api_key
                    else:
                        continue
                else:
                    logger.error("API密钥解密失败，请重新输入")
                    continue
            except Exception as e:
                logger.error(f"验证API密钥时出错: {e}")
                if input("继续使用此API密钥？(y/n): ").lower() == 'y':
                    return api_key
                continue
        else:
            logger.warning("您输入的是明文API密钥，建议使用api_crypto.py进行加密以提高安全性")
            if input("现在是否加密此API密钥？(y/n): ").lower() == 'y':
                try:
                    encrypted = api_crypto.encrypt(api_key)
                    if encrypted:
                        logger.info(f"API密钥已加密: {encrypted}")
                        if input("是否使用加密后的API密钥？(y/n): ").lower() == 'y':
                            return encrypted
                        # 如果用户选择不使用加密的密钥，继续使用明文
                except Exception as e:
                    logger.error(f"加密API密钥失败: {e}")
                    
            # 用户选择使用明文或加密失败，继续使用明文
            logger.info("将使用明文API密钥")
            return api_key

@dataclass
class Config:
    api_key: str
    model_id: str
    gemini_fallback_model_id: str
    openai_fallback_model_id: str
    api_mode: str
    api_base_url: str
    api_endpoint: str
    # 模型生成参数
    temperature: float
    top_p: float
    max_output_tokens: int
    top_k: int
    frequency_penalty: float
    presence_penalty: float
    thinking_budget_tokens: int # 新增：思考预算Token数量 (Vertex AI Gemini)，0表示关闭思考模式
    # 翻译行为配置
    translation_mode: int
    max_text_length: int
    context_max_count: int
    short_text_threshold: int
    lang_detection_threshold: float
    # 网络和请求配置
    tcp_connector: Dict
    timeout: Dict
    network_check: Dict
    api_health_check: Dict # 新增此行，用于API健康检查配置
    # 网络配置
    request_min_interval: float
    # 日志和调试
    debug_mode: bool
    log_info_max: int
    log_other_max: int
    # GUI配置
    show_gui_progress: bool
    # 文本过滤配置
    common_symbols: str
    illegal_chars: str
    # 安全设置
    safety_settings: Dict
    # 语言相关新增配置
    language_detection_cache_size: int = 100  # 语言检测缓存大小
    translation_cache_size: int = 50  # 翻译结果缓存大小
    same_language_match_threshold: float = 0.5  # 检测翻译结果与原文相似度的阈值
    # 本地缓存配置
    use_local_cache: bool = True  # 是否启用本地缓存
    cache_priority: bool = True  # True=优先使用缓存，False=优先使用大模型
    local_cache_path: str = "translation_cache.db"  # 缓存文件路径
    cache_max_entries: int = 1000  # 最大缓存条目数
    cache_write_delay: float = 2.0  # 缓存写入延迟（秒）
    cache_auto_save: bool = True  # 是否自动保存缓存
    language_families: Dict = field(default_factory=lambda: {
        "cjk": ["zh", "ja", "ko"],
        "european": ["en", "fr", "de", "es", "it", "pt", "ru"],
        "indic": ["hi", "bn", "ur"],
        "southeast_asian": ["th", "vi", "id", "ms", "km", "lo"], # 新增
        "semitic": ["ar", "he"]                                # 新增
    })  # 语言家族分组
    language_specific_settings: Dict = field(default_factory=dict)  # 每种语言的特定设置
    language_detection: Dict = field(default_factory=lambda: { # 更新默认值
        "ambiguity_factor": 1.4,
        "hint_bias": 0.2,
        "prob_weight": 0.7,
        "feature_weight": 0.3,
        "short_text_prob_weight": 0.4,
        "short_text_feature_weight": 0.6,
        "min_char_threshold": 10
    })
    # 通用语气符号配置
    universal_punctuation: Dict = field(default_factory=lambda: {
        "question_marks": {
            "universal": ["?", "？"],
            "latin": ["?"],
            "cjk": ["？"],
        },
        "exclamation_marks": {
            "universal": ["!", "！"],
            "latin": ["!"],
            "cjk": ["！"],
        }
    })  # 通用标点符号配置
    ko_zh_detection: Dict = field(default_factory=lambda: { # 新增字段
        "enabled": True,
        "ko_specific_ratio_threshold": 0.3,
        "lang_feature_score_threshold": 0.3,
        "feature_dominance_ratio": 2.0,
        "cjk_feature_score_threshold": 0.35
    })
    translation_quality: Dict = field(default_factory=lambda: { # 新增字段
        "min_char_ratio": 0.2,
        "default_feature_dominance_ratio": 2.0
    })

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项的值，如果不存在则返回默认值
        
        Args:
            key: 配置项的键
            default: 默认值
            
        Returns:
            Any: 配置项的值或默认值
        """
        try:
            value = getattr(self, key, default)
            return value
        except Exception as e:
            logger.error(f"获取配置项 {key} 时出错: {e}")
            return default

def deep_merge_configs(base: Dict, update: Dict) -> Dict:
    """递归深度合并两个配置字典，保留嵌套结构
    
    Args:
        base: 基础配置字典（将被更新）
        update: 更新的配置字典（优先级更高）
        
    Returns:
        Dict: 合并后的配置字典
    """
    for key, value in update.items():
        # 如果是字典类型且在基础配置中也是字典，递归合并
        if isinstance(value, dict) and key in base and isinstance(base[key], dict):
            base[key] = deep_merge_configs(base[key], value)
        else:
            # 否则直接更新
            base[key] = value
    return base

def _to_yaml_compatible(data: Any) -> Any:
    """将数据转换为 ruamel.yaml 兼容的格式，特别是处理多行字符串。"""
    if isinstance(data, dict):
        return {k: _to_yaml_compatible(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [_to_yaml_compatible(item) for item in data]
    elif isinstance(data, str) and "\\n" in data: # 假设包含 \\n 的是多行字符串
        return PreservedScalarString(data.replace("\\n", "\\n"))
    return data

def _merge_ruamel_data(target: Any, source: Any):
    """
    递归合并 'source' 到 'target'。
    'target' 是 ruamel.yaml 加载的数据 (CommentedMap, CommentedSeq)。
    'source' 是普通的 Python dict/list。
    """
    if isinstance(source, dict):
        if not isinstance(target, dict): # 如果目标不是字典，则无法合并，直接替换
            return _to_yaml_compatible(source)
        for key, value in source.items():
            if key in target:
                target[key] = _merge_ruamel_data(target[key], value)
            else:
                target[key] = _to_yaml_compatible(value)
        return target
    elif isinstance(source, list):
        # 对于列表，通常是直接替换，或者需要更复杂的合并逻辑（这里简化为替换）
        return _to_yaml_compatible(source)
    else:
        return _to_yaml_compatible(source)

def save_config(config: Union[Dict, Config], filename=CONFIG_FILE) -> bool: # 修正类型提示
    """保存配置到文件"""
    try:
        # 导入配置管理模块
        from config_management import save_main_config
        config_dict = asdict(config) if isinstance(config, Config) else config
        # 使用配置管理模块的函数保存
        if save_main_config(config_dict, filename):
            return True
        else:
            logger.error(f"使用config_management保存配置失败")
            return False
    except ImportError:
        logger.error("无法导入config_management模块，使用内置保存方法")
        config_dict = asdict(config) if isinstance(config, Config) else config
        
        try:
            yaml_loader = YAML()
            yaml_loader.preserve_quotes = True
            yaml_loader.indent(mapping=2, sequence=4, offset=2)
            
            with open(filename, "w", encoding="utf-8-sig") as f:
                yaml_loader.dump(config_dict, f)
            
            logger.info(f"配置已保存到 {filename}")
            return True
        except Exception as e:
            logger.error(f"保存配置到 {filename} 失败: {e}")
            # 备用保存方式
            try:
                with open(filename, "w", encoding="utf-8-sig") as f:
                    yaml.dump(config_dict, f, allow_unicode=True, default_flow_style=False, sort_keys=False)
                logger.info(f"配置已通过备用方式保存到 {filename}")
                return True
            except Exception as e2:
                logger.error(f"备用保存方式也失败: {e2}")
                return False

def load_mode_config() -> Dict:
    """加载模式配置文件，包含翻译模式、语言特征、语气词等"""
    # 先声明全局变量
    global MODE_CONFIG_FILE
    
    try:
        # 导入配置管理模块
        from config_management import generate_default_mode_config, get_default_mode_config_dict, complete_language_features_and_tones_in_dict, is_path_writable
    except ImportError:
        logger.error("无法导入config_management模块，请确保它存在于同一目录")
        sys.exit(1)
        
    # 检查模式配置文件路径是否可写
    if not is_path_writable(MODE_CONFIG_FILE):
        logger.warning(f"无法写入模式配置文件路径 {MODE_CONFIG_FILE}，将尝试使用备用路径。")
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, ".multitranslator")
        try:
            os.makedirs(app_data_dir, exist_ok=True)
            backup_mode_config_file = os.path.join(app_data_dir, os.path.basename(MODE_CONFIG_FILE))
            logger.info(f"将使用备用模式配置文件路径: {backup_mode_config_file}")
            # 如果备用位置已有文件，我们将使用该文件
            if os.path.exists(backup_mode_config_file):
                try:
                    with open(backup_mode_config_file, "r", encoding="utf-8-sig") as f:
                        yaml_loader = YAML()
                        mode_config = yaml_loader.load(f)
                    if isinstance(mode_config, dict):
                        logger.info(f"已从备用路径 {backup_mode_config_file} 加载模式配置")
                        # 将MODE_CONFIG_FILE设置为备用路径，以便后续操作使用正确的文件
                        MODE_CONFIG_FILE = backup_mode_config_file
                        # 转换为普通字典，确保后续处理的一致性
                        mode_config = dict(mode_config)
                        # 确保配置完整性
                        mode_config = complete_language_features_and_tones_in_dict(mode_config)
                        logger.info("语言模式配置已加载。")
                        return mode_config
                except Exception as e:
                    logger.error(f"从备用路径加载模式配置失败: {e}")
            else:
                # 如果备用位置没有文件，全局修改MODE_CONFIG_FILE指向备用位置
                MODE_CONFIG_FILE = backup_mode_config_file
                logger.info(f"已将模式配置文件路径修改为备用路径: {MODE_CONFIG_FILE}")
        except Exception as e:
            logger.error(f"创建备用配置目录失败: {e}")
        
    if not os.path.exists(MODE_CONFIG_FILE):
        logger.warning(f"模式配置文件 {MODE_CONFIG_FILE} 不存在，正在生成默认配置...")
        try:
            # 生成默认配置
            if generate_default_mode_config(force_overwrite=True):
                logger.info(f"已成功生成默认模式配置文件: {MODE_CONFIG_FILE}")
            else:
                logger.error(f"生成默认模式配置文件失败，使用内存中的默认配置")
                # 使用config_management中的默认配置
                default_mode_config = get_default_mode_config_dict()
                return complete_language_features_and_tones_in_dict(default_mode_config)
        except Exception as e:
            logger.error(f"生成默认模式配置文件时出错: {e}")
            logger.warning(f"将使用内存中的默认配置")
            default_mode_config = get_default_mode_config_dict()
            return complete_language_features_and_tones_in_dict(default_mode_config)

    try:
        with open(MODE_CONFIG_FILE, "r", encoding="utf-8-sig") as f:
            yaml_loader = YAML()
            mode_config = yaml_loader.load(f)
            if not isinstance(mode_config, dict):
                logger.error(f"模式配置文件 {MODE_CONFIG_FILE} 内容格式不正确或为空")
                logger.warning(f"将使用内存中的默认配置")
                default_mode_config = get_default_mode_config_dict()
                return complete_language_features_and_tones_in_dict(default_mode_config)
            
            # 转换为普通字典，确保后续处理的一致性
            mode_config = dict(mode_config)
    except Exception as e:
        logger.error(f"加载模式配置文件 {MODE_CONFIG_FILE} 失败: {e}")
        logger.warning(f"将使用内存中的默认配置")
        default_mode_config = get_default_mode_config_dict()
        return complete_language_features_and_tones_in_dict(default_mode_config)

    # 确保配置完整性
    mode_config = complete_language_features_and_tones_in_dict(mode_config)
    logger.info("语言模式配置已加载。")
    return mode_config

def get_language_mode_config() -> Dict:
    """获取语言模式配置 (现在直接调用 load_mode_config)"""
    config = load_mode_config()
    # 不再调用 complete_language_features_and_tones 或 save_mode_config
    # 这些逻辑现在由 config_management.py 在生成时处理
    if not config: # load_mode_config 失败时会 sys.exit，但以防万一
        logger.error("无法加载语言模式配置，程序将退出。")
        sys.exit(1)
    logger.info("语言模式配置已加载。")
    return config

def complete_language_features_and_tones(mode_config: Dict) -> Dict:
    """补全语言特征和语气词
    
    尝试补全配置中缺失的语言特征和语气词定义，为未来扩展到更多语言提供支持。
    
    Args:
        mode_config: 模式配置字典
        
    Returns:
        Dict: 补全后的模式配置
    """
    # 确保基础键存在
    required_keys = ['tone_particles', 'language_features', 'special_language_groups', 'special_language_pairs']
    for key in required_keys:
        if key not in mode_config:
            mode_config[key] = {}
            logger.debug(f"在mode_config中创建缺失的键: {key}")
    
    # 收集所有需要支持的语言代码
    required_langs = set()
    for mode in mode_config['translation_modes'].values():
        source_code = mode.get('source_code')
        target_code = mode.get('target_code')
        if source_code:
            required_langs.add(source_code)
        if target_code:
            required_langs.add(target_code)
            
    logger.debug(f"需要支持的语言代码: {required_langs}")
    
    # 默认语言特征配置 - 只包括常见语言
    default_language_features = {
        "zh": {
            "pattern": r"[\u4E00-\u9FFF]",  # 中文汉字
            "exclusive": [],  # 没有排他性特征，避免误判
            "desc": "中文字符",
            "question_pattern": r"[?？]",
            "exclamation_pattern": r"[!！]"
        },
        "ko": {
            "pattern": r"[\uAC00-\uD7AF]",  # 韩文谚文
            "exclusive": [],  # 没有排他性特征，避免误判
            "desc": "韩文字符",
            "question_pattern": r"[?？]",
            "exclamation_pattern": r"[!！]",
            "unique_features": ["요", "니다", "이다", "음", "군요", "네요", "아요", "어요", "ㅋㅋ", "ㅎㅎ"]  # 韩文特有词尾和语气词
        },
        "ja": {
            "pattern": r"[\u3040-\u309F\u30A0-\u30FF\u31F0-\u31FF]",  # 日文假名（平假名+片假名）
            "exclusive": [],  # 没有排他性特征，避免误判
            "desc": "日文字符",
            "question_pattern": r"[?？]",
            "exclamation_pattern": r"[!！]"
        },
        "en": {
            "pattern": r"[a-zA-Z]",  # 英文字母
            "exclusive": [],  # 没有排他性特征，避免误判
            "desc": "英文字符",
            "question_pattern": r"[?]",
            "exclamation_pattern": r"[!]"
        }
    }
    
    # 默认语气词配置
    default_tone_particles = {
        "zh": "[哈哈|嘿嘿|呵呵|啦|呀|呀|哟|哦]",  # 中文语气词
        "ko": "[ㅋ|ㅎ|아|네|헤]",  # 韩文语气词
        "ja": "[ｗ笑]+",  # 日文语气词
        "en": "[lol|haha|hehe|yeah]"  # 英文语气词
    }
    
    # 为每个所需语言补充语言特征
    existing_langs = set(mode_config['language_features'].keys())
    
    for lang in required_langs:
        # 1. 补充语言特征
        if lang not in existing_langs:
            # 使用默认特征配置（如果有）
            if lang in default_language_features:
                mode_config['language_features'][lang] = default_language_features[lang].copy()
                logger.debug(f"为语言 {lang} 添加默认语言特征配置")
            else:
                # 为新语言添加一个通用的语言特征模式
                mode_config['language_features'][lang] = {
                    "pattern": r"[\w\p{L}]",  # 使用通用的字母和Unicode字符类
                    "exclusive": [],
                    "desc": f"{lang}语言字符",
                    "question_pattern": r"[?]",
                    "exclamation_pattern": r"[!]"
                }
                logger.debug(f"为语言 {lang} 添加通用语言特征配置")
        else:
            # 如果已存在但配置不完整，补充默认值
            feature = mode_config['language_features'][lang]
            if "pattern" not in feature:
                if lang in default_language_features:
                    feature["pattern"] = default_language_features[lang]["pattern"]
                else:
                    feature["pattern"] = r"[\w\p{L}]"  # 通用模式
                
            if "exclusive" not in feature:
                feature["exclusive"] = []
                
            if "desc" not in feature:
                if lang in default_language_features:
                    feature["desc"] = default_language_features[lang]["desc"]
                else:
                    feature["desc"] = f"{lang}语言字符"
                    
            # 确保有问号和感叹号模式
            if "question_pattern" not in feature:
                if lang in default_language_features:
                    feature["question_pattern"] = default_language_features[lang]["question_pattern"]
                else:
                    feature["question_pattern"] = r"[?]"
                    
            if "exclamation_pattern" not in feature:
                if lang in default_language_features:
                    feature["exclamation_pattern"] = default_language_features[lang]["exclamation_pattern"]
                else:
                    feature["exclamation_pattern"] = r"[!]"
    
        # 2. 补充语气词配置
        if lang not in mode_config['tone_particles']:
            # 使用默认配置，如果有的话
            if lang in default_tone_particles:
                mode_config['tone_particles'][lang] = default_tone_particles[lang]
                logger.debug(f"为语言 {lang} 添加默认语气词配置")
            else:
                # 对于未知语言，使用通用模式
                mode_config['tone_particles'][lang] = r"[\w\p{P}]+"
                logger.debug(f"为未知语言 {lang} 添加通用语气词配置")
    
    # 添加或更新语言组配置
    default_language_groups = {
        "cjk": {
            "languages": ["zh", "ja", "ko"],
            "strict_detection": False,  # 改为False，避免严格检测引起的误判
            "desc": "中日韩语言组"
        },
        "latin": {
            "languages": ["en", "fr", "es", "de", "it", "pt", "nl", "sv", "da", "no", "fi"],
            "strict_detection": False,
            "desc": "拉丁语系"
        },
        "slavic": {
            "languages": ["ru", "uk", "be", "bg", "pl", "cs", "sk", "sl", "hr", "bs", "sr", "mk"],
            "strict_detection": False,
            "desc": "斯拉夫语系"
        },
        "indic": {
            "languages": ["hi", "bn", "pa", "gu", "mr", "ta", "te", "kn", "ml"],
            "strict_detection": False,
            "desc": "印度语系"
        },
        "semitic": {
            "languages": ["ar", "he", "mt", "am", "ti"],
            "strict_detection": False,
            "desc": "闪米特语系"
        }
    }
    
    # 更新语言组配置
    for group_name, group_config in default_language_groups.items():
        if group_name not in mode_config['special_language_groups']:
            mode_config['special_language_groups'][group_name] = group_config
            logger.debug(f"添加语言组 {group_name}")
        else:
            # 更新现有语言组中的languages列表，合并而不是替换
            existing_group = mode_config['special_language_groups'][group_name]
            if "languages" in existing_group:
                existing_languages = set(existing_group["languages"])
                new_languages = set(group_config.get("languages", []))
                # 合并语言列表
                combined_languages = list(existing_languages.union(new_languages))
                existing_group["languages"] = combined_languages
                # 确保其他键有默认值
                if "strict_detection" not in existing_group:
                    existing_group["strict_detection"] = group_config.get("strict_detection", False)
                if "desc" not in existing_group:
                    existing_group["desc"] = group_config.get("desc", f"{group_name}语言组")
    
    # 添加通配符语言对 - 适用于所有语言
    if "*-*" not in mode_config['special_language_pairs']:
        mode_config['special_language_pairs']["*-*"] = {
            "max_attempts": 2,
            "desc": "通用语言对配置",
            "skip_source_detection": False,  # 默认不跳过源语言检测
            "min_char_ratio": 0.2           # 默认最小字符比例
        }
    
    # 处理特殊语言对配置
    # 确保不同语言组之间和组内的语言对都有特殊处理配置
    all_langs = set()
    for group_info in mode_config['special_language_groups'].values():
        all_langs.update(group_info.get("languages", []))
    
    # 为每个已定义的语言对添加基本配置
    for mode_id, mode_info in mode_config['translation_modes'].items():
        source = mode_info.get("source_code")
        target = mode_info.get("target_code")
        
        if source and target and source != target:
            pair_key = f"{source}-{target}"
            if pair_key not in mode_config['special_language_pairs']:
                # 判断源语言和目标语言所属的语言组
                source_in_cjk = source in mode_config['special_language_groups'].get('cjk', {}).get('languages', [])
                target_in_cjk = target in mode_config['special_language_groups'].get('cjk', {}).get('languages', [])
                
                # 为不同类型的语言对设置不同的配置
                if source_in_cjk and target_in_cjk:
                    # CJK语言之间的互译
                    mode_config['special_language_pairs'][pair_key] = {
                        "max_attempts": 2,
                        "desc": f"{source}-{target}互译配置",
                        "skip_source_detection": True,  # CJK互译特殊处理
                        "min_char_ratio": 0.3
                    }
                elif source_in_cjk or target_in_cjk:
                    # CJK与其他语系的互译
                    mode_config['special_language_pairs'][pair_key] = {
                        "max_attempts": 2,
                        "desc": f"{source}-{target}互译配置",
                        "skip_source_detection": False,  # 需检测源语言
                        "min_char_ratio": 0.15,
                        "allow_short_text_mismatch": True  # 对短文本宽松处理
                    }
                else:
                    # 其他语言对
                    mode_config['special_language_pairs'][pair_key] = {
                        "max_attempts": 1,
                        "desc": f"{source}-{target}互译配置",
                        "skip_source_detection": False,
                        "min_char_ratio": 0.1
                    }
                    
                logger.debug(f"为语言对 {pair_key} 添加默认配置")
    
    # 为所有现有语言组之间的互译创建基本配置
    groups = list(mode_config['special_language_groups'].keys())
    for source_group in groups:
        for target_group in groups:
            if source_group != target_group:
                group_pair = f"{source_group}-{target_group}"
                if group_pair not in mode_config['special_language_pairs']:
                    # 为不同类型的语言组对设置不同的配置
                    if source_group == "cjk" or target_group == "cjk":
                        # CJK与其他语组的互译
                        mode_config['special_language_pairs'][group_pair] = {
                            "desc": f"{source_group}与{target_group}语言组互译配置",
                            "min_char_ratio": 0.15,
                            "allow_short_text_mismatch": True  # 对短文本宽松处理
                        }
                    else:
                        # 其他语言组对
                        mode_config['special_language_pairs'][group_pair] = {
                            "desc": f"{source_group}与{target_group}语言组互译配置",
                            "min_char_ratio": 0.1
                        }
    
    # 添加语言组内部配置
    for group_name, group_info in mode_config['special_language_groups'].items():
        group_languages = group_info.get("languages", [])
        
        # 为组内语言添加通配符配置
        wildcard_key = f"{group_name}-*"
        reverse_wildcard_key = f"*-{group_name}"
        
        if wildcard_key not in mode_config['special_language_pairs']:
            mode_config['special_language_pairs'][wildcard_key] = {
                "desc": f"{group_name}语言组对外互译",
                "min_char_ratio": 0.15,
                "max_attempts": 2
            }
            
        if reverse_wildcard_key not in mode_config['special_language_pairs']:
            mode_config['special_language_pairs'][reverse_wildcard_key] = {
                "desc": f"外部语言翻译到{group_name}语言组",
                "min_char_ratio": 0.15,
                "max_attempts": 2
            }
    
    return mode_config

def save_mode_config(mode_config: Dict, filename=MODE_CONFIG_FILE): # 添加 filename 参数
    """保存模式配置到文件"""
    try:
        # 导入配置管理模块
        from config_management import save_mode_config_file, is_path_writable
        # 使用配置管理模块的函数保存
        if save_mode_config_file(mode_config, filename):
            return True
        else:
            logger.error(f"使用config_management保存模式配置失败")
            # 尝试备用保存方法
            try:
                # 检查路径是否可写
                if not is_path_writable(filename):
                    user_home = os.path.expanduser("~")
                    app_data_dir = os.path.join(user_home, ".multitranslator")
                    try:
                        os.makedirs(app_data_dir, exist_ok=True)
                        backup_filename = os.path.join(app_data_dir, os.path.basename(filename))
                        logger.warning(f"无法写入 {filename}，将使用备用路径: {backup_filename}")
                        filename = backup_filename
                    except Exception as e:
                        logger.error(f"创建备用配置目录失败: {e}")
                        return False
                
                # 确保目录存在
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                
                yaml_loader = YAML()
                yaml_loader.preserve_quotes = True
                yaml_loader.indent(mapping=2, sequence=4, offset=2)
                
                with open(filename, "w", encoding="utf-8-sig") as f:
                    yaml_loader.dump(mode_config, f)
                
                logger.info(f"模式配置已保存到 {filename}")
                return True
            except Exception as e:
                logger.error(f"保存模式配置到 {filename} 失败: {e}")
                return False
    except ImportError:
        logger.error("无法导入config_management模块，使用内置保存方法")
        try:
            # 检查路径是否可写
            try:
                # 尝试简单的可写性检查
                if not os.access(os.path.dirname(filename) or ".", os.W_OK):
                    user_home = os.path.expanduser("~")
                    app_data_dir = os.path.join(user_home, ".multitranslator")
                    try:
                        os.makedirs(app_data_dir, exist_ok=True)
                        backup_filename = os.path.join(app_data_dir, os.path.basename(filename))
                        logger.warning(f"无法写入 {filename}，将使用备用路径: {backup_filename}")
                        filename = backup_filename
                    except Exception as e:
                        logger.error(f"创建备用配置目录失败: {e}")
                        return False
            except Exception: # 为路径检查添加except块
                # 如果检查失败，我们仍然尝试正常保存
                pass 
                
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            yaml_loader = YAML()
            yaml_loader.preserve_quotes = True
            yaml_loader.indent(mapping=2, sequence=4, offset=2)
            
            with open(filename, "w", encoding="utf-8-sig") as f:
                yaml_loader.dump(mode_config, f)
            
            logger.info(f"模式配置已保存到 {filename}")
            return True
        except Exception as e: # 为外部try添加except块
            logger.error(f"保存模式配置到 {filename} 失败: {e}")
            # 备用保存方式
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                
                with open(filename, "w", encoding="utf-8-sig") as f:
                    yaml.dump(mode_config, f, allow_unicode=True, default_flow_style=False, sort_keys=False)
                logger.info(f"模式配置已通过备用方式保存到 {filename}")
                return True
            except Exception as e2:
                logger.error(f"备用保存方式也失败: {e2}")
                return False

# 日志配置
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 添加网络和API状态管理类，实现缓存机制
class ServiceManager:
    """服务管理类，管理网络连接和API服务状态，提供缓存机制"""
    
    def __init__(self, config):
        """初始化服务管理器
        
        Args:
            config: 配置对象
        """
        self.config = config
        
        # 网络状态缓存
        self.network_status = {
            "connected": None,  # 网络连接状态
            "last_check": 0,    # 上次检查时间
            "check_interval": 30  # 检查间隔 (秒)
        }
        
        # API健康状态缓存
        self.api_health_cache = {}    # 键: "{api_mode}:{model_id}:{api_key前10位}"
        self.api_cache_lifetime = 300  # API缓存有效期 (秒)
        
        # 读取配置
        if hasattr(config, 'network_check') and isinstance(config.network_check, dict):
            network_check = config.network_check
            if 'check_interval' in network_check:
                self.network_status["check_interval"] = float(network_check.get('check_interval', 30))
        
    def is_network_connected(self, force_check=False) -> bool:
        """检查网络连接状态，带缓存机制
    
    Args:
            force_check: 是否强制检查而忽略缓存

    Returns:
            网络是否连接的布尔值
        """
        current_time = time.time()
        
        # 如果缓存有效且不是强制检查，直接返回缓存的结果
        if (not force_check and 
            self.network_status["connected"] is not None and 
            current_time - self.network_status["last_check"] < self.network_status["check_interval"]):
            logger.debug(f"使用缓存的网络状态: {'已连接' if self.network_status['connected'] else '未连接'}")
            return self.network_status["connected"]
            
        # 执行实际的网络检查
        is_connected = self._check_network_impl()
        
        # 更新缓存
        self.network_status["connected"] = is_connected
        self.network_status["last_check"] = current_time
        
        return is_connected
        
    def _check_network_impl(self) -> bool:
        """实际执行网络检查"""
        network_check_config = self.config.get("network_check", {})
        host = network_check_config.get("host", "*******")
        port = network_check_config.get("port", 53)
        # 明确设置超时时间，优先使用配置值，否则使用默认值2秒
        timeout = network_check_config.get("timeout", 2) 

        try:
            # 创建socket对象并设置超时
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout) # 直接为这个socket实例设置超时
            sock.connect((host, port))
            sock.close() # 关闭连接
            return True
        except socket.timeout: # 明确捕获超时异常
            logger.warning(f"网络检查超时 ({timeout}秒) 连接到 {host}:{port}")
            return False
        except socket.error as ex:
            logger.warning(f"网络检查失败 连接到 {host}:{port}: {ex}")
            return False

    async def is_api_healthy(self, api_mode, api_key, model_id=None, api_base_url=None, force_check=False) -> tuple[bool, str]:
        """检查API健康状态（带缓存）
        
        Args:
            api_mode: API模式
            api_key: API密钥
            model_id: 模型ID (可选)
            api_base_url: API基础URL (可选)
            force_check: 是否强制重新检查
            
        Returns:
            tuple: (is_healthy, message)
        """
        # 如果未提供API密钥，返回False
        if not api_key:
            return False, "未提供API密钥"
            
        # 获取实际的API密钥
        real_api_key = get_real_api_key(api_key)
        if not real_api_key:
            return False, "API密钥无效或无法解密"
            
        # 如果缓存有效且不强制检查，使用缓存
        cache_key = f"{api_mode}:{model_id}:{real_api_key[:10]}"
        if not force_check and cache_key in self.api_health_cache:
            cached_result = self.api_health_cache[cache_key]
            if time.time() - cached_result["timestamp"] < self.api_cache_lifetime:
                return cached_result["is_healthy"], cached_result["message"]
                
        # 执行实际检查
        is_healthy, message = await check_api_health(api_mode, api_key, model_id, api_base_url)
        
        # 更新缓存
        self.api_health_cache[cache_key] = {
            "is_healthy": is_healthy,
            "message": message,
            "timestamp": time.time()
        }
        
        return is_healthy, message
        
    def reset_network_cache(self):
        """重置网络状态缓存"""
        self.network_status["connected"] = None
        self.network_status["last_check"] = 0
        
    def reset_api_cache(self):
        """重置API状态缓存"""
        logger.debug("重置API状态缓存")
        self.api_health_cache.clear()

async def check_api_health(api_mode, api_key, model_id=None, api_base_url=None) -> tuple[bool, str]:
    """检查API健康状态
    
    Args:
        api_mode: API模式 (gemini/openai)
        api_key: API密钥
        model_id: 模型ID (可选)
        api_base_url: API基础URL (可选)
        
    Returns:
        tuple: (is_healthy, message)
    """
    # 确保有有效的API密钥
    if not api_key:
        return False, "缺少API密钥"
        
    # 获取实际的API密钥（如果是加密的）
    real_api_key = get_real_api_key(api_key)
    if not real_api_key:
        return False, f"无法解密API密钥，请检查密钥格式是否正确"
    
    # 设置API健康检查配置默认值
    timeout_total = 10
    timeout_connect = 5
    timeout_sock_connect = 5
    timeout_sock_read = 8
    test_prompt = "Hello, API check"
    
    try:
        # 设置API健康检查配置
        from config_management import get_default_config_dict
        default_config = get_default_config_dict()
        if "api_health_check" in default_config:
            api_health_check = default_config["api_health_check"]
            timeout_total = api_health_check.get("timeout_total", timeout_total)
            timeout_connect = api_health_check.get("timeout_connect", timeout_connect)
            timeout_sock_connect = api_health_check.get("timeout_sock_connect", timeout_sock_connect)
            timeout_sock_read = api_health_check.get("timeout_sock_read", timeout_sock_read)
            test_prompt = api_health_check.get("test_prompt", test_prompt)
    except Exception as e:
        logger.error(f"加载API健康检查配置失败: {e}")
    
    # 创建超时对象
    timeout = aiohttp.ClientTimeout(
        total=timeout_total,
        connect=timeout_connect,
        sock_connect=timeout_sock_connect,
        sock_read=timeout_sock_read
    )
    
    # 记录API密钥前几个字符用于调试（不要记录完整密钥）
    safe_key_display = real_api_key[:5] + "..." if len(real_api_key) > 5 else "无效密钥"
    logger.debug(f"使用API密钥进行健康检查: {safe_key_display}")
    
    # 尝试与API建立连接
    try:
        if api_mode.lower() == "gemini":
            # 针对Gemini API的健康检查
            base_url = "https://generativelanguage.googleapis.com"
            model_to_check = model_id or "gemini-1.5-pro-latest"  # 使用更稳定的模型进行检查
            url = f"{base_url}/v1beta/models/{model_to_check}:generateContent?key={real_api_key}"
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 首先尝试获取模型信息以验证API密钥
                model_list_url = f"{base_url}/v1beta/models?key={real_api_key}"
                async with session.get(model_list_url) as response:
                    if response.status == 200:
                        logger.debug(f"Gemini API密钥验证成功，状态码: {response.status}")
                        return True, "API密钥有效，可以访问Gemini模型"
                    elif response.status == 400:
                        resp_text = await response.text()
                        if "API key not valid" in resp_text:
                            logger.error(f"Gemini API密钥无效: {safe_key_display}")
                            return False, "API密钥无效，请检查您的Gemini API密钥"
                        else:
                            return False, f"API错误: {response.status} - {resp_text[:100]}"
                    else:
                        return False, f"API服务器返回错误: {response.status}"
                        
        elif api_mode.lower() == "openai":
            # 针对OpenAI API的健康检查
            base_url = api_base_url or "https://api.openai.com"
            url = f"{base_url}/v1/models"
            headers = {
                "Authorization": f"Bearer {real_api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        return True, "API密钥有效，可以访问OpenAI模型"
                    elif response.status == 401:
                        logger.error(f"OpenAI API密钥无效: {safe_key_display}")
                        return False, "API密钥无效，请检查您的OpenAI API密钥"
                    else:
                        resp_text = await response.text()
                        return False, f"API服务器返回错误: {response.status} - {resp_text[:100]}"
        else:
            return False, f"不支持的API模式: {api_mode}"
    except asyncio.TimeoutError:
        return False, "API连接超时，请检查您的网络连接"
    except Exception as e:
        return False, f"API健康检查失败: {str(e)}"



# --- 辅助函数：获取语言族 ---
def get_language_family(lang_code: str, language_families: Dict) -> Optional[str]:
    """获取语言所属的语言族"""
    for family, langs in language_families.items():
        if lang_code in langs:
            return family
    return None

# --- 新增：通用语言处理工具函数 ---
def language_utils(mode: str, text: str = None, original_text: str = None, translated_text: str = None,
                  source_lang_code: str = None, target_lang_code: str = None,
                  detected_source_lang: str = None, is_original: bool = True, 
                  config: Optional[Config] = None, mode_config: Optional[Dict] = None,
                  hint_lang: Optional[str] = None, supported_langs: Optional[Dict] = None) -> Any:
    """
    通用语言处理函数，集成多种语言相关功能:
    - 语言检测 (detect)
    - 符号保留检查 (check_symbols)
    - 翻译质量评估 (assess_quality)
    - 字符数比例检查 (char_ratio)
    - 获取语言特征 (get_features)
    
    Args:
        mode: 操作模式，可选值: 'detect', 'check_symbols', 'assess_quality', 'char_ratio', 'get_features'
        text: 要处理的文本 (用于语言检测)
        original_text: 原始文本 (用于符号检查和质量评估)
        translated_text: 翻译后的文本 (用于符号检查和质量评估)
        source_lang_code: 源语言代码
        target_lang_code: 目标语言代码
        detected_source_lang: 检测到的源语言代码
        is_original: 是否为原文
        config: 配置对象
        mode_config: 模式配置对象
        hint_lang: 提示语言代码
        supported_langs: 支持的语言字典 (用于语言检测)
    
    Returns:
        根据模式返回不同类型的结果
    """
    global language_detection_cache  # 全局缓存变量
    
    # 语言检测模式
    if mode == 'detect' and text:
        if not supported_langs:
            logger.warning("缺少supported_langs参数，无法执行语言检测")
            return "unknown"
            
        cache_key = f"{text[:100]}|{config.lang_detection_threshold if config else 85.0}|{config.short_text_threshold if config else 20}|{is_original}"
        if language_detection_cache is not None:
            cached_result = language_detection_cache.get(cache_key)
            if cached_result:
                logger.info(f"【语言检测】使用缓存结果: {cached_result}")
                return cached_result
                
        # 提取语言特征
        language_features = mode_config.get("language_features", {}) if mode_config else {}
        language_families = getattr(config, 'language_families', {}) if config else {}
        threshold = getattr(config, 'lang_detection_threshold', 85.0) if config else 85.0
        short_text_threshold = getattr(config, 'short_text_threshold', 20) if config else 20
        
        # 执行特征匹配 (预计算所有支持语言的特征信息)
        feature_results = {}
        compiled_patterns = {} 
        
        for lang_code, feature in language_features.items():
            if lang_code not in supported_langs: 
                continue
            try:
                # 预编译主模式
                if lang_code not in compiled_patterns:
                    if "pattern" not in feature:
                        logger.warning(f"语言 {lang_code} 没有定义 pattern，跳过特征检测")
                        feature_results[lang_code] = {"matches": False, "excludes_violated": True, "match_score": 0, "desc": feature.get("desc", f"{lang_code} 语言")}
                        continue
                    try:
                        compiled_patterns[lang_code] = {"main": regex.compile(feature["pattern"]), "exclusive": []}
                    except regex.error as e:
                        logger.error(f"语言 {lang_code} 的主正则表达式错误: {e}")
                        feature_results[lang_code] = {"matches": False, "excludes_violated": True, "match_score": 0, "desc": feature.get("desc", f"{lang_code} 语言")}
                        continue

                # 预编译排他模式
                if "exclusive" in feature and isinstance(feature["exclusive"], list) and not compiled_patterns[lang_code]["exclusive"]:
                    for i, excl_pattern_str in enumerate(feature["exclusive"]):
                        try:
                            compiled_patterns[lang_code]["exclusive"].append(regex.compile(excl_pattern_str))
                        except regex.error as e:
                            logger.error(f"语言 {lang_code} 的第 {i+1} 个排他性正则表达式错误: {e}")
                
                pattern = compiled_patterns[lang_code]["main"]
                exclusive_patterns = compiled_patterns[lang_code]["exclusive"]
                
                matches = bool(pattern.search(text))
                excludes_violated = any(excl.search(text) for excl in exclusive_patterns)
                
                match_score = 0
                if matches and not excludes_violated and len(text) > 0:
                    try:
                        matched_content = regex.findall(pattern, text)
                        match_len = sum(len(m) if isinstance(m, str) else 1 for m in matched_content)
                        match_score = min(match_len / len(text), 1.0)
                    except regex.error as e:
                        logger.error(f"计算语言 {lang_code} 的 match_score 时正则错误: {e}")

                feature_results[lang_code] = {
                    "matches": matches, 
                    "excludes_violated": excludes_violated,
                    "desc": feature.get("desc", f"{lang_code} 语言"),
                    "match_score": match_score
                }
            except Exception as e:
                logger.error(f"处理语言 {lang_code} 的特征时发生意外错误: {e}")
                feature_results[lang_code] = {"matches": False, "excludes_violated": True, "match_score": 0, "desc": f"{lang_code} 语言"}
        
        # 调用 pycld2 进行检测
        cld2_results = []
        try:
            is_reliable, text_bytes, detected_details = cld2.detect(text)
            logger.debug(f"pycld2 检测结果: is_reliable={is_reliable}, details={detected_details}")
            
            for name, code, percent, score in detected_details:
                if code != 'un' and code in supported_langs:
                    cld2_results.append({"lang": code, "prob": percent / 100.0}) 

        except cld2.error as e:
            logger.error(f"pycld2 检测异常: {e}，将仅依赖特征匹配")
            cld2_results = [] 

        # 调用通用决策逻辑
        is_short_text = len(text) < short_text_threshold
        final_lang = run_universal_decision_logic(
            main_detector_results=cld2_results,
            feature_results=feature_results,
            is_short_text=is_short_text,
            main_detector_threshold=threshold / 100.0,
            language_families=language_families,
            supported_langs=supported_langs,
            sample_text=text, 
            config=config,
            mode_config=mode_config,
            hint_lang=hint_lang
        )

        # 缓存结果
        if language_detection_cache is not None:
            language_detection_cache.put(cache_key, final_lang)
            logger.debug(f"【缓存更新】保存语言检测结果: {final_lang}")
            
        return final_lang
        
    # 符号保留检查模式
    elif mode == 'check_symbols' and original_text and translated_text and source_lang_code and target_lang_code:
        if not config:
            logger.warning("缺少config参数，无法执行符号保留检查")
            return True  # 默认通过
            
        # 获取源语言和目标语言所属的语言家族
        source_family = get_language_family(source_lang_code, config.language_families)
        target_family = get_language_family(target_lang_code, config.language_families)
        
        # 获取标点符号配置
        source_question_marks = []
        target_question_marks = []
        source_exclamation_marks = []
        target_exclamation_marks = []
        
        # 处理标点符号
        for family, punct_type in [(source_family, "question_marks"), (target_family, "question_marks"), 
                                  (source_family, "exclamation_marks"), (target_family, "exclamation_marks")]:
            # 先检查语言特定设置
            lang_code = source_lang_code if family == source_family else target_lang_code
            lang_specific = config.language_specific_settings.get(lang_code, {})
            
            if punct_type in lang_specific:
                if family == source_family and punct_type == "question_marks":
                    source_question_marks = lang_specific[punct_type]
                elif family == target_family and punct_type == "question_marks":
                    target_question_marks = lang_specific[punct_type]
                elif family == source_family and punct_type == "exclamation_marks":
                    source_exclamation_marks = lang_specific[punct_type]
                elif family == target_family and punct_type == "exclamation_marks":
                    target_exclamation_marks = lang_specific[punct_type]
            else:
                # 获取语系标点配置
                family_key = family if family else "universal"
                if family_key in config.universal_punctuation.get(punct_type, {}):
                    if family == source_family and punct_type == "question_marks":
                        source_question_marks = config.universal_punctuation[punct_type][family_key]
                    elif family == target_family and punct_type == "question_marks":
                        target_question_marks = config.universal_punctuation[punct_type][family_key]
                    elif family == source_family and punct_type == "exclamation_marks":
                        source_exclamation_marks = config.universal_punctuation[punct_type][family_key]
                    elif family == target_family and punct_type == "exclamation_marks":
                        target_exclamation_marks = config.universal_punctuation[punct_type][family_key]
                else:
                    # 使用通用标点
                    universal_marks = config.universal_punctuation[punct_type].get("universal", [])
                    if family == source_family and punct_type == "question_marks":
                        source_question_marks = universal_marks
                    elif family == target_family and punct_type == "question_marks":
                        target_question_marks = universal_marks
                    elif family == source_family and punct_type == "exclamation_marks":
                        source_exclamation_marks = universal_marks
                    elif family == target_family and punct_type == "exclamation_marks":
                        target_exclamation_marks = universal_marks
        
        # 检查原文和译文中是否包含问号和感叹号
        has_source_question = any(mark in original_text for mark in source_question_marks)
        has_target_question = any(mark in translated_text for mark in target_question_marks)
        has_source_exclamation = any(mark in original_text for mark in source_exclamation_marks)
        has_target_exclamation = any(mark in translated_text for mark in target_exclamation_marks)
        
        # 安全获取语气词配置
        tone_particles = mode_config.get('tone_particles', {}) if mode_config else {}
        default_tone = r'[\w\p{P}]+'
        
        # 获取语言特定的语气词模式
        source_tone_pattern = tone_particles.get(source_lang_code, default_tone)
        target_tone_pattern = tone_particles.get(target_lang_code, default_tone)
        
        # 检查语气词
        try:
            original_has_tone = bool(regex.search(source_tone_pattern, original_text))
            translated_has_tone = bool(regex.search(target_tone_pattern, translated_text))
        except regex.error as e:
            logger.error(f"检查语气词时正则表达式错误: {e}")
            original_has_tone = False
            translated_has_tone = False

        # 获取语言特定的符号保留配置
        language_pair = f"{source_lang_code}-{target_lang_code}"
        special_pairs = mode_config.get("special_language_pairs", {}) if mode_config else {}
        
        # 默认的检查行为
        check_question = True
        check_exclamation = True
        check_tone = True
        
        # 如果有语言对特定的配置，使用该配置
        if language_pair in special_pairs:
            pair_config = special_pairs[language_pair]
            check_question = pair_config.get("check_question", True)
            check_exclamation = pair_config.get("check_exclamation", True)
            check_tone = pair_config.get("check_tone", True)
            
        # 检查问号是否保留
        if check_question and has_source_question and not has_target_question:
            logger.warning("翻译结果丢失疑问语气")
            return False
            
        # 检查感叹号或语气是否保留
        if check_exclamation and has_source_exclamation and not (has_target_exclamation or translated_has_tone):
            logger.warning("翻译结果丢失强调语气，且无等效语气词")
            return False
            
        # 对短文本跳过字符数比例检查
        total_original_chars = len(original_text.strip())
        if total_original_chars < config.short_text_threshold:
            logger.debug(f"短文本 ({total_original_chars} 字符) 跳过字符数比例检查")
            return True

        # 安全获取语言特征模式用于字符计数
        default_pattern = r'[^\s\p{P}]'
        
        # 获取特定语言的字符匹配模式
        language_features = mode_config.get("language_features", {}) if mode_config else {}
        source_pattern = language_features.get(source_lang_code, {}).get("pattern", default_pattern)
        target_pattern = language_features.get(target_lang_code, {}).get("pattern", default_pattern)

        try:
            original_text_chars = len(regex.findall(source_pattern, original_text))
            
            # 当源语言和目标语言相同或属于相同语系时，使用更通用的字符检测
            if source_lang_code == target_lang_code or source_family == target_family:
                translated_text_chars = len(regex.findall(r'[^\s]', translated_text))
            else:
                translated_text_chars = len(regex.findall(target_pattern, translated_text))
                
        except regex.error as e:
            logger.error(f"字符数检查时正则表达式错误: {e}")
            return True

        # 检查翻译是否有实质内容
        if original_text_chars > 0 and translated_text_chars == 0:
            logger.warning(f"翻译结果文字内容为空: 原文 {original_text_chars} 个字符，翻译 {translated_text_chars} 个字符")
            return False
            
        # 当原文与译文使用同一语言时，应该放宽字符比例要求
        if source_lang_code == target_lang_code:
            logger.debug("源语言和目标语言相同，放宽字符比例要求")
            return True

        # 获取字符比例配置
        translation_quality_config = getattr(config, 'translation_quality', {})
        default_min_char_ratio = translation_quality_config.get("min_char_ratio", 0.2)
        
        # 检查是否有语言对特定的字符比例配置
        if language_pair in special_pairs and "min_char_ratio" in special_pairs[language_pair]:
            min_char_ratio = special_pairs[language_pair]["min_char_ratio"]
        else:
            # 查找源语言和目标语言所属的语言组
            special_groups = mode_config.get("special_language_groups", {}) if mode_config else {}
            source_groups = []
            target_groups = []
            
            for group_name, group_info in special_groups.items():
                languages = group_info.get("languages", [])
                if source_lang_code in languages:
                    source_groups.append(group_name)
                if target_lang_code in languages:
                    target_groups.append(group_name)
            
            # 检查语言组对的配置
            min_char_ratio = default_min_char_ratio
            for s_group in source_groups:
                for t_group in target_groups:
                    group_pair = f"{s_group}-{t_group}"
                    if group_pair in special_pairs and "min_char_ratio" in special_pairs[group_pair]:
                        min_char_ratio = special_pairs[group_pair]["min_char_ratio"]
                        break
                else:
                    continue
                break
            
        # 检查翻译结果是否过短
        detection_config = getattr(config, 'language_detection', {})
        min_char_threshold = detection_config.get("min_char_threshold", 10)
        min_chars_required = original_text_chars * min_char_ratio
        
        if original_text_chars > min_char_threshold and translated_text_chars < min_chars_required:
            logger.warning(f"翻译结果文字内容显著过少: 原文 {original_text_chars} 个字符，翻译 {translated_text_chars} 个字符，"
                          f"最小要求 {min_chars_required:.1f} 个字符")
            return False

        return True
        
    # 翻译质量评估模式
    elif mode == 'assess_quality' and original_text and translated_text and source_lang_code and target_lang_code:
        issues = []
        score = 1.0
        
        # 检查译文是否为空或通用错误提示
        if not translated_text.strip():
            issues.append("翻译结果为空")
            return "较差", 0.0, issues
        
        common_error_indicators = ["[翻译失败]", "error:", " ", "i apologize, but i cannot"]
        for indicator in common_error_indicators:
            if indicator.lower() in translated_text.lower():
                issues.append(f"翻译结果包含错误指示符: '{indicator}'")
                score *= 0.1
        
        # 检查源语言残留
        contains_source = False
        if detected_source_lang:
            # 通过重用language_utils函数的内部逻辑检测语言残留
            source_pattern = ""
            language_features = mode_config.get("language_features", {}) if mode_config else {}
            if detected_source_lang in language_features and "pattern" in language_features[detected_source_lang]:
                source_pattern = language_features[detected_source_lang]["pattern"]
                try:
                    contains_source = bool(regex.search(source_pattern, translated_text))
                except regex.error:
                    contains_source = False
        
        if contains_source:
            issues.append(f"翻译结果可能残留源语言 ({detected_source_lang}) 内容")
            score *= 0.7 

        # 检查符号和内容保留
        symbol_check = language_utils('check_symbols', 
                               original_text=original_text, 
                               translated_text=translated_text,
                               source_lang_code=source_lang_code, 
                               target_lang_code=target_lang_code,
                               config=config, 
                               mode_config=mode_config)
        
        if not symbol_check:
            issues.append("翻译结果可能丢失关键语气、符号或内容不足")
            score *= 0.65

        # 文本相似度检查
        from difflib import SequenceMatcher
        
        def clean_text(text):
            # 统一空白字符
            text = regex.sub(r'\s+', ' ', text).strip()
            # 去除常见标点符号
            text = regex.sub(r'[\p{P}\p{S}]', '', text)
            # 统一大小写（如果是拉丁字母）
            text = text.lower()
            return text
            
        def calculate_similarity(text1, text2):
            # 清理文本
            clean_text1 = clean_text(text1)
            clean_text2 = clean_text(text2)
            
            # 对于较短的文本，直接使用SequenceMatcher
            if len(clean_text1) < 100 and len(clean_text2) < 100:
                return SequenceMatcher(None, clean_text1, clean_text2).ratio()
                
            # 对于较长文本，使用n-gram方法
            def get_ngrams(text, n=3):
                return set(text[i:i+n] for i in range(max(0, len(text) - n + 1)))
                
            # 计算3-gram和2-gram的Jaccard相似度
            ngrams1 = get_ngrams(clean_text1, 3)
            ngrams2 = get_ngrams(clean_text2, 3)
            
            if not ngrams1 or not ngrams2:
                # 文本太短，改用2-gram
                ngrams1 = get_ngrams(clean_text1, 2)
                ngrams2 = get_ngrams(clean_text2, 2)
                
            # 计算Jaccard相似度
            intersection = len(ngrams1.intersection(ngrams2))
            union = len(ngrams1.union(ngrams2))
            
            return intersection / union if union > 0 else 0.0
            
        similarity = calculate_similarity(original_text, translated_text)
        
        high_similarity_threshold = getattr(config, 'same_language_match_threshold', 0.5) if config else 0.5
        
        # 检查是否真的翻译成了目标语言
        if detected_source_lang != target_lang_code and similarity > high_similarity_threshold - 0.1:
             issues.append(f"翻译结果与原文相似度过高 ({similarity:.2f})，可能翻译质量不高或过于字面。")
             score *= 0.75
        elif detected_source_lang == target_lang_code and similarity < 0.3:
             issues.append(f"同语言翻译结果与原文相似度过低 ({similarity:.2f})，可能内容丢失。")
             score *= 0.6

        # 重复词/短语检测
        import collections
        words = regex.findall(r'\b\w+\b', translated_text.lower())
        if len(words) > 10:
            word_counts = collections.Counter(words)
            most_common = word_counts.most_common(1)
            if most_common and most_common[0][1] > len(words) * 0.3 and most_common[0][1] > 3:
                issues.append(f"翻译结果中词语 '{most_common[0][0]}' 重复次数过多")
                score *= 0.8

        # 根据分数和问题数量判断最终标签
        final_label = "良好"
        if score < 0.6 or len(issues) >= 3:
            final_label = "较差"
        elif score < 0.85 or len(issues) >= 1:
            final_label = "一般"
            
        return final_label, score, issues
        
    # 字符比例检查模式
    elif mode == 'char_ratio' and original_text and translated_text and source_lang_code and target_lang_code:
        # 获取语言特征用于字符计数
        default_pattern = r'[^\s\p{P}]'
        language_features = mode_config.get("language_features", {}) if mode_config else {}
        source_pattern = language_features.get(source_lang_code, {}).get("pattern", default_pattern)
        target_pattern = language_features.get(target_lang_code, {}).get("pattern", default_pattern)
        
        try:
            original_text_chars = len(regex.findall(source_pattern, original_text))
            translated_text_chars = len(regex.findall(target_pattern, translated_text))
            
            # 获取字符比例配置
            translation_quality_config = getattr(config, 'translation_quality', {}) if config else {}
            min_char_ratio = translation_quality_config.get("min_char_ratio", 0.2)
            
            # 计算实际比例
            if original_text_chars > 0:
                actual_ratio = translated_text_chars / original_text_chars
            else:
                actual_ratio = 0
                
            return {
                "original_chars": original_text_chars,
                "translated_chars": translated_text_chars,
                "min_ratio": min_char_ratio,
                "actual_ratio": actual_ratio,
                "is_sufficient": actual_ratio >= min_char_ratio
            }
        except regex.error as e:
            logger.error(f"字符比例检查时正则表达式错误: {e}")
            return {
                "original_chars": len(original_text),
                "translated_chars": len(translated_text),
                "min_ratio": 0.2,
                "actual_ratio": len(translated_text) / len(original_text) if len(original_text) > 0 else 0,
                "is_sufficient": True,  # 出错时保守处理
                "error": str(e)
            }
    
    # 获取语言特征模式
    elif mode == 'get_features' and source_lang_code:
        language_features = mode_config.get("language_features", {}) if mode_config else {}
        if source_lang_code in language_features:
            return language_features[source_lang_code]
        return None
        
    else:
        logger.error(f"无效的语言处理模式: {mode} 或参数不足")
        return None

# --- 新的通用决策逻辑函数 ---
def run_universal_decision_logic(
    main_detector_results: List[Dict], 
    feature_results: Dict, 
    is_short_text: bool, 
    main_detector_threshold: float, 
    language_families: Dict, 
    supported_langs: Dict,
    sample_text: str, 
    config: Config,
    mode_config: Dict = None, # 新增：mode_config参数
    hint_lang: Optional[str] = None # 提示语言参数
) -> str:
    """
    通用的决策逻辑，结合主检测器 (pycld2) 和特征匹配结果。

    Args:
        main_detector_results: 来自 pycld2 的候选列表 [{"lang": code, "prob": 0-1 probability}, ...]
        feature_results: 预计算的特征匹配结果 {lang_code: {"matches": bool, "excludes_violated": bool, "match_score": float, "desc": str}}
        is_short_text: 是否为短文本
        main_detector_threshold: 主检测器置信度基础阈值 (用于判断是否可靠)
        language_families: 语族配置
        supported_langs: 支持的语言字典 (用于检查语言是否有效)
        sample_text: 用于特定语族规则分析的原始文本片段
        config: 主配置对象
        mode_config: 模式配置对象，包含语言对消歧规则
        hint_lang: 可选的提示语言，如果提供，会给予该语言更高的初始分数。

    Returns:
        最终判定的语言代码 (str) 或 "unknown"
    """
    combined_scores = {}
    
    # 从配置中获取语言检测参数
    detection_config = getattr(config, 'language_detection', {})
    hint_bias = detection_config.get("hint_bias", 0.2)
    prob_weight = detection_config.get("prob_weight", 0.7)
    feature_weight = detection_config.get("feature_weight", 0.3)
    short_text_prob_weight = detection_config.get("short_text_prob_weight", 0.4)
    short_text_feature_weight = detection_config.get("short_text_feature_weight", 0.6)
    ambiguity_factor = detection_config.get("ambiguity_factor", 1.4)
    
    # 如果有提示语言，并且该语言在支持的语言中，给予一个较高的初始偏置分数
    if hint_lang and hint_lang in supported_langs and hint_lang in feature_results and not feature_results[hint_lang]["excludes_violated"]:
        combined_scores[hint_lang] = hint_bias
        logger.debug(f"决策逻辑：应用提示语言 '{hint_lang}' 偏置: +{hint_bias}")

    # 1. 结合主检测器和特征计算初始分数
    for candidate in main_detector_results:
        lang = candidate["lang"]
        prob = candidate["prob"] 
        
        if lang not in supported_langs or lang == 'un': 
            continue

        feature_info = feature_results.get(lang, {"matches": False, "excludes_violated": True, "match_score": 0})
        match_score = feature_info["match_score"]
        excludes_violated = feature_info["excludes_violated"]

        if excludes_violated:
            # 如果提示语言被排他规则排除，移除偏置
            if lang == hint_lang and lang in combined_scores:
                logger.debug(f"决策逻辑：提示语言 '{lang}' 因违反排他规则被排除，移除偏置")
                combined_scores.pop(lang) # 或者设置为负数标记排除
            else:
                # combined_scores[lang] = -1 # 标记明确排除，但下方已有移除逻辑
                pass # 后面会处理
            logger.debug(f"语言 {lang} 因违反排他规则被评估为低优先级或排除")
            continue 
            
        # 根据文本长度选择不同的权重
        actual_prob_weight = short_text_prob_weight if is_short_text else prob_weight
        actual_feature_weight = short_text_feature_weight if is_short_text else feature_weight

        # 基础分数计算
        current_score_base = (prob * actual_prob_weight) + (match_score * actual_feature_weight)
        
        # 如果当前语言是提示语言，确保其分数不低于基础分和偏置（如果未被排除）
        if lang == hint_lang and lang in combined_scores:
            combined_scores[lang] = max(combined_scores[lang], current_score_base + hint_bias) 
        elif lang == hint_lang: # 提示语言但之前未加入（例如cld2未检出但特征匹配）
            combined_scores[lang] = current_score_base + hint_bias
        else: # 非提示语言
            combined_scores[lang] = max(combined_scores.get(lang, 0), current_score_base)

    # 2. 基于特征匹配补充候选 (主要针对那些 cld2 可能漏掉但特征明显的语言)
    for lang, feature_info in feature_results.items():
        if lang not in supported_langs or feature_info["excludes_violated"]:
            continue
        
        # 如果该语言仅靠特征匹配，且未在cld2结果中，给一个基础分
        if lang not in combined_scores and feature_info["matches"] and feature_info["match_score"] > 0.1:
            base_feature_score = feature_info["match_score"] * 0.5 # 特征匹配的权重可以低一些
            if lang == hint_lang: # 如果这个纯特征匹配的是提示语言
                combined_scores[lang] = base_feature_score + hint_bias
                logger.debug(f"基于特征和提示补充候选: {lang} (score: {combined_scores[lang]:.4f})")
            else:
                combined_scores[lang] = base_feature_score
                logger.debug(f"基于特征补充候选: {lang} (score: {combined_scores[lang]:.4f})")
        # 如果已在combined_scores中 (可能来自cld2或hint)，但特征分更高，可以考虑更新，但要小心
        # 暂时不处理这种情况，避免过于复杂的分数调整

    # 移除排他规则排除的语言，并确保分数非负
    valid_scores = {}
    for lang, score in combined_scores.items():
        if feature_results.get(lang, {}).get("excludes_violated", False):
            logger.debug(f"决策逻辑：语言 {lang} 因确认违反排他规则最终被排除")
            continue
        valid_scores[lang] = max(0, score)
    combined_scores = valid_scores

    if not combined_scores:
        logger.warning("决策逻辑：没有可靠的语言候选")
        return "unknown"

    # 3. 排序和选择最佳候选
    # 排序时，主要看分数，如果分数相同，cld2的原始概率可以作为次要排序依据
    sorted_langs = sorted(
        combined_scores.items(), 
        key=lambda item: (item[1], next((c['prob'] for c in main_detector_results if c['lang'] == item[0]), 0)), 
        reverse=True
    )
    
    logger.debug(f"决策逻辑：综合评分排序 (含提示 '{hint_lang}'): {[(l, round(s,3)) for l,s in sorted_langs]}")
    best_lang, best_score = sorted_langs[0]

    # 4. 处理歧义 (适用于所有语言对，而不仅是特定语族)
    resolved_lang = None
    if len(sorted_langs) > 1:
        second_lang, second_score = sorted_langs[1]
        # 调整歧义判断阈值，如果最高分是hint_lang，可以适当放宽，允许它胜出
        actual_ambiguity_factor = ambiguity_factor
        if best_lang == hint_lang:
            actual_ambiguity_factor = ambiguity_factor * 0.85  # 如果最佳是提示语言，降低歧义因子
        
        if best_score < second_score * actual_ambiguity_factor and best_score > 0.1: 
            logger.debug(f"决策逻辑：检测到歧义: {best_lang} ({best_score:.4f}) vs {second_lang} ({second_score:.4f})")
            
            # 新增：通用语言对消歧处理
            pair_key = f"{best_lang}-{second_lang}"
            reverse_pair_key = f"{second_lang}-{best_lang}"
            
            # 从mode_config中获取语言对消歧规则
            if mode_config and 'pair_disambiguation_rules' in mode_config:
                pair_rules = mode_config['pair_disambiguation_rules']
                
                # 尝试找到适用的语言对规则
                rule_config = None
                used_pair_key = None
                
                if pair_key in pair_rules:
                    rule_config = pair_rules[pair_key]
                    used_pair_key = pair_key
                elif reverse_pair_key in pair_rules:
                    rule_config = pair_rules[reverse_pair_key]
                    used_pair_key = reverse_pair_key
                
                if rule_config and rule_config.get("enabled", False):
                    logger.debug(f"决策逻辑：应用语言对消歧规则 '{used_pair_key}'")
                    
                    # 获取配置中的特征主导比例，如果不存在则使用默认值
                    translation_quality_config = getattr(config, 'translation_quality', {})
                    default_feature_dominance_ratio = translation_quality_config.get(
                        "default_feature_dominance_ratio", 2.0)
                    
                    # 通用处理：检查特征分数比例
                    feature_dominance_ratio = rule_config.get("feature_dominance_ratio", default_feature_dominance_ratio)
                    best_feature_score = feature_results.get(best_lang, {}).get("match_score", 0)
                    second_feature_score = feature_results.get(second_lang, {}).get("match_score", 0)
                    
                    if best_feature_score > second_feature_score * feature_dominance_ratio:
                        logger.debug(f"语言对规则：{best_lang} 特征明显占优")
                        resolved_lang = best_lang
                    elif second_feature_score > best_feature_score * feature_dominance_ratio:
                        logger.debug(f"语言对规则：{second_lang} 特征明显占优")
                        resolved_lang = second_lang
                    else:
                        # 检查语言特定标记
                        lang_markers = {}
                        for lang in [best_lang, second_lang]:
                            # 从语言特征配置中获取特定标记列表
                            lang_features = feature_results.get(lang, {})
                            unique_features = []
                            
                            # 从language_features中获取unique_features
                            if mode_config and 'language_features' in mode_config:
                                lang_config = mode_config['language_features'].get(lang, {})
                                unique_features = lang_config.get('unique_features', [])
                            
                            if unique_features:
                                # 检查文本中是否存在这些特征
                                marker_count = 0
                                for marker in unique_features:
                                    try:
                                        if regex.search(marker, sample_text):
                                            marker_count += 1
                                    except regex.error:
                                        pass  # 忽略无效正则
                                
                                if marker_count > 0:
                                    lang_markers[lang] = marker_count
                                    logger.debug(f"语言 {lang} 找到 {marker_count} 个特征标记")
                        
                        # 基于特定标记判断
                        if lang_markers:
                            most_markers = max(lang_markers.items(), key=lambda x: x[1], default=(None, 0))
                            if most_markers[0] and most_markers[1] > 0:
                                logger.debug(f"基于特定标记选择语言: {most_markers[0]} (标记数: {most_markers[1]})")
                                resolved_lang = most_markers[0]
                        
                        # 如果仍未解决，使用特定的规则优先级
                        if not resolved_lang and "preferred_lang" in rule_config:
                            preferred = rule_config["preferred_lang"]
                            if preferred in [best_lang, second_lang]:
                                logger.debug(f"基于规则优先级选择语言: {preferred}")
                                resolved_lang = preferred

    # 最终确定语言
    if resolved_lang:
        logger.debug(f"决策逻辑：歧义解决后选择语言: {resolved_lang}")
        return resolved_lang
    else:
        logger.debug(f"决策逻辑：无歧义或未解决歧义，选择得分最高的语言: {best_lang}")
        return best_lang

class Translator:
    def __init__(self, config: Config, root: tk.Tk):
        """初始化翻译器实例"""
        self.config = config
        self.root = root
        # 修正函数名，确保模式配置能够正确加载
        try:
            self.mode_config = get_language_mode_config()
        except NameError:
            # 如果get_language_mode_config不存在，使用原有的load_mode_config
            self.mode_config = load_mode_config()
            logger.warning("使用旧的配置加载函数load_mode_config，建议更新为get_language_mode_config")
        
        # 初始化语言检测缓存 (保持不变)
        global language_detection_cache
        if language_detection_cache is None and hasattr(config, 'language_detection_cache_size'):
            language_detection_cache = LRUCache(config.language_detection_cache_size)
            logger.info(f"【初始化】语言检测缓存，容量: {config.language_detection_cache_size}")
        
        # 初始化翻译缓存 (保持不变)
        self.translation_cache = LRUCache(getattr(config, 'translation_cache_size', 50))
        logger.info(f"【初始化】翻译缓存，容量: {getattr(config, 'translation_cache_size', 50)}")
        
        # 初始化本地缓存管理器
        from cache_manager import CacheManager
        self.cache_manager = CacheManager(config)
        logger.info(f"【初始化】本地翻译缓存管理器，数据库路径: {config.local_cache_path}")
        
        # 初始化服务管理器
        self.service_manager = ServiceManager(config)
        logger.debug("Translator: ServiceManager initialized")
        
        # 初始化翻译模式菜单项
        self.mode_menu_items = self._build_mode_menu_items()
        
        # 保持原有历史记录初始化逻辑
        self.history = {mode: [] for mode in self.mode_config["translation_modes"].keys()}
        self.translation_history = []  # 存储最近的翻译记录，用于上下文理解
        
        # 维护翻译状态
        self.translate_lock = threading.Lock()
        self.replace_lock = threading.Lock()
        self._in_progress = False # 初始化内部状态
        self._suppress_keyboard = False # 初始化内部状态
        self.last_replace_time = 0
        self.last_request_time = 0
        
        # 初始化事件循环，用于异步操作
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        # 存储当前GUI状态
        self.config_panel_open = False
        self.drag_data = {"x": 0, "y": 0, "dragging": False}
        
        # 初始化GUI组件
        self.frames = {}
        
        # 状态管理
        self.current_status = "就绪"
        self.api_health_status = {
            "healthy": None,
            "message": "尚未检查API状态",
            "last_check": 0
        }
        self.last_api_health_check = 0
        
        # 允许使用的颜色
        self.allowed_colors = ["white", "red", "green", "blue", "gray", "yellow", 
                            "purple", "pink", "orange", "cyan", "black", "brown"]
        
        # 创建主窗口
        logger.debug("Translator: Attempting to create main window...")
        self.create_main_window()
        logger.debug("Translator: Main window created.")
        
        # 启动键盘监听线程
        self.keyboard_thread = None
        self.should_exit = threading.Event()
        logger.debug("Translator: Attempting to setup keyboard listener...")
        self.setup_keyboard_listener()
        logger.debug("Translator: Keyboard listener setup.")

        # 添加窗口关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 异步事件循环和线程控制
        self._shutdown_event = threading.Event()
        self.loop_thread = threading.Thread(target=self.run_async_loop, daemon=True)
        logger.debug("Translator: Attempting to start async loop thread...")
        self.loop_thread.start()
        logger.debug("Translator: Async loop thread started.")
        
        # 启动时检查API健康状态
        asyncio.run_coroutine_threadsafe(self.check_api_health(), self.loop)
        logger.debug("Translator: __init__ completed.")

    def _build_mode_menu_items(self) -> Dict:
        """构建并同步翻译模式菜单项"""
        items = {0: {"desc": "设置"}, "00": {"desc": "清空上下文"}}
        for mode_id, mode_data in self.mode_config["translation_modes"].items():
            desc = f"{mode_data['source_lang']}-{mode_data['target_lang']}" + (f"-{mode_data['style']}" if mode_data['style'] else "")
            items[mode_id] = {"desc": desc}
        return items

    def run_async_loop(self):
        """运行异步事件循环"""
        asyncio.set_event_loop(self.loop)
        try:
            while not self._shutdown_event.is_set():
                try:
                    self.loop.run_forever()
                except Exception as e:
                    logger.error(f"异步事件循环异常: {e}，尝试重启循环")
                    time.sleep(1)
                    # 如果循环异常，确保它被重置
                    if not self.loop.is_closed():
                        self.loop.close()
                    self.loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(self.loop)
        finally:
            # 确保循环被正确关闭
            if not self.loop.is_closed():
                self.loop.close()
            logger.debug("异步事件循环已关闭")
            
    def shutdown(self):
        """关闭翻译器并释放资源"""
        logger.info("正在关闭翻译器...")
        try:
            # 停止事件循环
            if hasattr(self, 'loop') and self.loop.is_running():
                self.loop.stop()
                
            # 释放其他资源
            self.should_exit.set()
            
            # 关闭键盘监听线程
            if self.keyboard_thread and self.keyboard_thread.is_alive():
                logger.debug("等待键盘监听线程结束...")
                self.keyboard_thread.join(timeout=1.0)
                
            # 重置缓存
            if hasattr(self, 'service_manager'):
                self.service_manager.reset_network_cache()
                self.service_manager.reset_api_cache()
                
            logger.debug("翻译器资源已释放")
        except Exception as e:
            logger.error(f"关闭翻译器时出错: {e}")
            
    @property
    def in_progress(self):
        with self.translate_lock:
            return self._in_progress

    @in_progress.setter
    def in_progress(self, value):
        with self.translate_lock:
            self._in_progress = value

    @property
    def suppress_keyboard(self):
        with self.translate_lock:
            return self._suppress_keyboard

    @suppress_keyboard.setter
    def suppress_keyboard(self, value):
        with self.translate_lock:
            self._suppress_keyboard = value

    def detect_language(self, text: str, is_original: bool = True) -> str:
        """检测文本语言"""
        # 提取支持的语言代码列表
        supported_lang_codes = set()
        for mode_data in self.mode_config.get("translation_modes", {}).values():
            if "source_code" in mode_data:
                 supported_lang_codes.add(mode_data["source_code"])
            if "target_code" in mode_data:
                 supported_lang_codes.add(mode_data["target_code"])
        # 转换为 language_utils 需要的格式 {code: {}}
        supported_langs_dict = {code: {} for code in supported_lang_codes}
        
        # 调用新的通用语言处理函数
        detected_lang_code = language_utils(
            mode='detect',
            text=text,
            is_original=is_original,
            config=self.config,
            mode_config=self.mode_config,
            hint_lang=None,
            supported_langs=supported_langs_dict
        )
        
        # 保存检测到的语言代码，用于后续处理，并记录一条综合日志
        if is_original:
            self.source_lang_code = detected_lang_code
            logger.info(f"检测到原文语言: {self.source_lang_code}")
        else:
            # 当检测目标语言时，逻辑可能需要调整？
            # 目前是直接用检测结果作为反向翻译的目标语言
            self.target_lang_code = detected_lang_code
            logger.info(f"检测到目标语言文本，识别为: {detected_lang_code} (用于反向翻译)")
            
        return detected_lang_code

    def get_filter_pattern(self, lang_code: str) -> str:
        """获取过滤模式，根据不同语言可能有不同的过滤规则
        
        Args:
            lang_code: 语言代码
            
        Returns:
            过滤正则表达式模式
        """
        # 首先检查语言特定的过滤模式
        language_features = self.mode_config.get("language_features", {})
        
        # 如果语言在特征定义中存在，并且有filter_pattern定义
        if lang_code in language_features and "filter_pattern" in language_features[lang_code]:
            lang_filter = language_features[lang_code]["filter_pattern"]
            logger.debug(f"使用语言 {lang_code} 特定的过滤模式: {lang_filter}")
            return lang_filter
            
        # 查找该语言所属的语言组，可能有组级别的过滤模式
        special_language_groups = self.mode_config.get("special_language_groups", {})
        for group_name, group_info in special_language_groups.items():
            if lang_code in group_info.get("languages", []) and "filter_pattern" in group_info:
                group_filter = group_info["filter_pattern"]
                logger.debug(f"使用语言组 {group_name} 的过滤模式: {group_filter}")
                return group_filter
                
        # 如果没有特定的过滤模式，直接使用通用的非法字符定义，不输出调试日志
        return self.config.illegal_chars

    def build_prompt(self, text: str, detected_lang: str) -> tuple[str, str]:
        """构建翻译提示词
        
        Args:
            text: 输入文本
            detected_lang: 检测到的语言代码
            
        Returns:
            构建的提示词和期望的语言代码
        """
        # 获取当前翻译模式配置
        mode_config = self.mode_config["translation_modes"].get(self.config.translation_mode, self.mode_config["translation_modes"][1])
        source_code = mode_config.get("source_code", "unknown")
        target_code = mode_config.get("target_code", "unknown")
        
        # 检查是否选择了相同语言的翻译方向
        if source_code == target_code:
            logger.warning(f"检测到翻译方向设置为相同语言: {source_code}->{target_code}，这可能导致翻译问题")
        
        # 检查输入文本的语言是否与翻译方向匹配
        if detected_lang == target_code:
            # 如果检测到的语言是目标语言，这通常意味着需要反向翻译或检查模式配置
            logger.debug(f"检测到输入语言 ({detected_lang}) 与当前模式定义的目标语言 ({target_code}) 一致，触发反向翻译流程。")

        # 构建翻译提示词
        source_lang = mode_config.get("source_lang", "未知语言")
        target_lang = mode_config.get("target_lang", "未知语言")
        style = mode_config.get("style", "")
        default_lang = mode_config.get("default_lang", source_lang)

        # 根据检测到的语言确定输入/输出语言及语码
        if detected_lang == source_code:
            input_lang = source_lang
            output_lang = target_lang
            input_code = source_code
            output_code = target_code
            style_instruction = f"，使用{style if style else '自然表达'}" if style else ""
            expected_lang_code = target_code
        elif detected_lang == target_code:
            input_lang = target_lang
            output_lang = source_lang
            input_code = target_code
            output_code = source_code
            style_instruction = "" # 反向翻译通常不带特定风格指令
            expected_lang_code = source_code
        else:
            # 未知语言情况
            input_lang = "未知语言" # 或者更具体的如 f"{detected_lang}语言"
            output_lang = default_lang # 默认输出到模式定义的源语言
            input_code = detected_lang
            output_code = mode_config.get("source_code", default_lang) # 确保有回退
            style_instruction = ""
            expected_lang_code = output_code

        # 新增：检查检测到的语言是否与最终的目标翻译语言相同
        if expected_lang_code is not None and detected_lang == expected_lang_code:
             logger.debug(f" 检测到输入文本语言 ({detected_lang}) 与最终确定的目标翻译语言 ({expected_lang_code}) 相同，检查逻辑或模式设置。")

        # 安全获取语气词配置
        tone_particles = self.mode_config.get('tone_particles', {})
        default_tone_pattern = r"[\w\p{P}]+" # 通用模式作为默认值
        
        source_tone = tone_particles.get(input_code, default_tone_pattern)
        target_tone = tone_particles.get(output_code, default_tone_pattern)
        
        if self.config.debug_mode:
            logger.debug(f"检测到语言: {detected_lang}, 输入语言: {input_lang}({input_code}), 输出语言: {output_lang}({output_code})")
            logger.debug(f"使用语气词 - 输入: {source_tone}, 输出: {target_tone}")

        # 格式化通用提示词模板
        formatted_universal_prompt = UNIVERSAL_PROMPT_TEMPLATE.format(
            input_lang=input_lang,
            output_lang=output_lang,
            style_instruction=style_instruction,
            source_lang=source_lang, # 模式定义的源语言名
            target_lang=target_lang, # 模式定义的目标语言名
            default_lang=default_lang, # 模式定义的默认输出语言名
            source_tone=source_tone,
            target_tone=target_tone,
        )

        # 记录思考模式状态
        thinking_budget = self.config.thinking_budget_tokens
        if "gemini-2.5" in self.config.model_id:
            if thinking_budget > 0:
                logger.debug(f"思考预算 (Thinking Budget) 当前设置: {thinking_budget} Tokens（思考模式已启用）")
            else:
                logger.debug(f"思考预算为0，思考模式已禁用")
        else:
            logger.debug(f"当前模型 {self.config.model_id} 不支持思考模式")

        context_max_count = max(self.config.context_max_count, 0)
        mode_history = self.history.get(self.config.translation_mode, [])
        if len(mode_history) > context_max_count:
            mode_history = mode_history[-context_max_count:]
        context_entries = mode_history
        if context_entries:
            logger.info(f"模式 {self.config.translation_mode} 当前上下文数量: {len(context_entries)}（最大: {context_max_count}）")
            context_list = [f"原文: {entry['source']}\n翻译: {entry['translation']}" for entry in context_entries]
            context_str = "\n".join(context_list)
            if len(context_str) > 2000:
                context_str = context_str[-2000:]
                logger.debug("上下文过长，已截取最后 2000 字符")
            context_str = f"以下是最近的上下文，请参考上下文翻译以保持一致性：\n{context_str}\n\n"
        else:
            context_str = ""

        processed_text = " ".join(line.strip() for line in text.splitlines() if line.strip())
        translation_task_instruction = f"将以下内容从{input_lang}翻译成{output_lang}{style_instruction}：\n{processed_text}"

        # 根据思考预算构建最终提示词
        if "gemini-2.5" in self.config.model_id and self.config.thinking_budget_tokens > 0:
            thinking_prefix = f"请你先一步一步地思考（你可以使用大约 {self.config.thinking_budget_tokens} tokens的预算进行思考）如何才能最好地遵循以下所有规则，并提供最佳翻译。\n"
            full_prompt = f"{thinking_prefix}\n{formatted_universal_prompt}\n\n{context_str}{translation_task_instruction}"
        else:
            full_prompt = f"{formatted_universal_prompt}\n\n{context_str}{translation_task_instruction}"
        
        logger.debug(f"发给大模型的完整提示词:\n{full_prompt}")
        
        if not hasattr(self, '_prompt_length_logged') or self._prompt_length_logged != full_prompt:
            logger.debug(f"【构建提示词】长度: {len(full_prompt)} 字符")
            self._prompt_length_logged = full_prompt
        
        if self.config.debug_mode and len(full_prompt) > 4000:
            logger.warning("提示词较长（>4000字符），可能接近模型输入限制")
        return full_prompt, expected_lang_code

    def filter_translation(self, text: str, filter_pattern: str) -> str:
        """过滤翻译结果"""
        # 使用实例变量跟踪过滤前文本记录，避免重复记录
        if not hasattr(self, '_filter_text_logged') or self._filter_text_logged != text:
            logger.debug(f"过滤前文本: {text}")
            self._filter_text_logged = text
        
        try:
            # 应用过滤模式
            filtered = regex.sub(filter_pattern, '', text)
            filtered = regex.sub(r'\s+', ' ', filtered).strip()
            
            # 记录过滤后的结果
            logger.debug(f"应用 filter_pattern {repr(filter_pattern)} 后: {filtered}")
            return filtered
        except regex.error as e:
            logger.error(f"正则表达式错误: {e}，返回原始文本")
            return text.strip()

    def check_symbol_retention(self, original: str, translated: str, target_lang_code: str, source_lang_code: str) -> bool:
        """检查翻译结果是否保留关键符号和语气
        
        Args:
            original: 原始文本
            translated: 翻译后的文本
            target_lang_code: 目标语言代码
            source_lang_code: 源语言代码
            
        Returns:
            bool: 如果翻译保留了关键符号和语气则返回True
        """
        # 使用通用语言处理函数
        return language_utils(
            mode='check_symbols',
            original_text=original,
            translated_text=translated,
            source_lang_code=source_lang_code,
            target_lang_code=target_lang_code,
            config=self.config,
            mode_config=self.mode_config
        )

    async def check_api_health(self):
        """检查API健康状态并更新状态信息"""
        try:
            # 使用服务管理器检查网络和API状态
            is_network_ok = self.service_manager.is_network_connected()
            if not is_network_ok:
                self.api_health_status = {
                    "healthy": False,
                    "message": "网络连接不可用",
                    "last_check": time.time()
                }
                logger.warning("网络连接不可用，API健康检查中止")
                return
            
            # 使用服务管理器检查API健康状态
            healthy, message = await self.service_manager.is_api_healthy(
                self.config.api_mode,
                self.config.api_key,
                self.config.model_id,
                self.config.api_base_url if self.config.api_mode == "openai" else None,
                force_check=True  # 强制重新检查，不使用缓存
            )
            
            if not healthy:
                logger.warning(f"API健康检查失败: {message}")
            else:
                logger.info(f"API健康检查成功: {message}")
            
            self.api_health_status = {
                "healthy": healthy,
                "message": message,
                "last_check": time.time()
            }
            
        except Exception as e:
            logger.error(f"API健康检查过程中出错: {e}")
            self.api_health_status = {
                "healthy": False,
                "message": f"检查过程错误: {e}",
                "last_check": time.time()
            }

    async def translate_text_async(self, prompt: str, detected_lang: str, original_text: str, expected_lang_code: str) -> str:
        """异步翻译文本
        
        Args:
            prompt: 提示词
            detected_lang: 检测到的语言代码
            original_text: 原始文本
            expected_lang_code: 期望的语言代码
            
        Returns:
            翻译后的文本，或者在最终失败时返回以 \"翻译失败：\" 开头的特定错误信息。
        """
        # 检查是否被中止
        if not self.in_progress:
            logger.info("检测到翻译被中止，主动退出翻译流程（translate_text_async-开始）")
            # 如果中止，也尝试恢复原文，以防万一之前的操作未完成
            # await self.replace_input_text_async(self.original_text if hasattr(self, 'original_text') else original_text)
            return "翻译已中止"

        # 缓存键：基于原文本和目标语言
        memory_cache_key = f"{self.config.translation_mode}-{detected_lang}-{expected_lang_code}-{hash(original_text)}"
        
        # 检查缓存优先级
        if self.config.cache_priority:
            # 优先查询本地缓存
            if self.config.use_local_cache:
                logger.debug("优先查询本地缓存")
                local_cached_result = self.cache_manager.get_translation(original_text, expected_lang_code, detected_lang)
                if local_cached_result:
                    logger.info("【本地缓存命中】翻译结果从本地数据库获取")
                    # 也更新内存缓存
                    self.translation_cache.put(memory_cache_key, local_cached_result)
                    return local_cached_result
            
            # 查询内存缓存
            memory_cached_result = self.translation_cache.get(memory_cache_key)
            if memory_cached_result:
                logger.info(f"【内存缓存命中】翻译结果来自内存缓存")
                return memory_cached_result
        else:
            # 仅检查内存缓存
            memory_cached_result = self.translation_cache.get(memory_cache_key)
            if memory_cached_result:
                logger.info(f"【内存缓存命中】翻译结果来自内存缓存")
                return memory_cached_result
            
            # 如果内存缓存未命中且允许使用本地缓存，则检查本地缓存
            if self.config.use_local_cache:
                local_cached_result = self.cache_manager.get_translation(original_text, expected_lang_code, detected_lang)
                if local_cached_result:
                    logger.info("【本地缓存命中】翻译结果从本地数据库获取")
                    # 也更新内存缓存
                    self.translation_cache.put(memory_cache_key, local_cached_result)
                    return local_cached_result

        original_temperature = self.config.temperature
        if not self.api_health_status.get("healthy", True):
            logger.warning(f"API服务可能不健康: {self.api_health_status.get('message', '未知原因')}，将尝试翻译")
        
        translation_attempted_with_fallback = False
        try:
            if not self.service_manager.is_network_connected():
                # 网络问题，直接恢复原文并提示
                logger.error("翻译失败：网络连接不可用 (translate_text_async)")
                await self.replace_input_text_async(original_text) 
                return "翻译失败：网络连接不可用"

            if not self.in_progress:
                logger.info("检测到翻译被中止，主动退出翻译流程（translate_text_async-网络后）")
                return "翻译已中止"
            
            # 第一次尝试使用主模型
            result = await self._translate_with_api(prompt, detected_lang, original_text, expected_lang_code, model_to_use=self.config.model_id, is_retry=False)

            # 如果第一次尝试失败 (返回了特定的失败前缀)
            if result.startswith("翻译失败：") and not ("网络连接不可用" in result or "翻译已中止" in result or "内容被API" in result):
                logger.warning(f"主模型 ({self.config.model_id}) 翻译失败: {result}。尝试使用备用模型。")
                translation_attempted_with_fallback = True
                fallback_model_id = self.config.gemini_fallback_model_id if self.config.api_mode == "gemini" else self.config.openai_fallback_model_id
                
                if not self.in_progress:
                    logger.info("检测到翻译被中止，主动退出翻译流程（translate_text_async-重试前）")
                    return "翻译已中止"

                result = await self._translate_with_api(prompt, detected_lang, original_text, expected_lang_code, model_to_use=fallback_model_id, is_retry=True)

            if not self.in_progress:
                logger.info("检测到翻译被中止，主动退出翻译流程（translate_text_async-API后）")
                return "翻译已中止"
            
            # 最终结果处理
            if result.startswith("翻译失败："):
                logger.error(f"翻译最终失败: {result}。原始文本将恢复到输入框。")
                await self.replace_input_text_async(original_text)
                # 对于网络错误或中止，GUI的错误提示可能已在replacement_translation中处理
                # 此处返回具体错误信息供上层判断
                return result 

            if result and not result == "翻译已中止": # 成功翻译
                # 先替换输入框内容，再异步执行缓存操作
                await self.replace_input_text_async(result)
                
                # 创建异步任务执行缓存操作（不等待完成）
                async def update_cache():
                    try:
                        # 1. 保存标准方向的内存缓存
                        self.translation_cache.put(memory_cache_key, result)
                        logger.info(f"【内存缓存更新】翻译结果已存入内存缓存")
                        
                        # 2. 同时保存反向内存缓存，以便下次反向翻译时可以直接使用
                        # 反向缓存键：mode-target_lang-source_lang-result_hash
                        reverse_memory_cache_key = f"{self.config.translation_mode}-{expected_lang_code}-{detected_lang}-{hash(result)}"
                        self.translation_cache.put(reverse_memory_cache_key, original_text)
                        logger.info(f"【内存缓存更新】反向翻译结果已存入内存缓存")
                        
                        # 3. 保存到本地缓存
                        if self.config.use_local_cache:
                            # 使用异步方法更新缓存
                            await self.cache_manager.async_add_translation(original_text, expected_lang_code, result, detected_lang)
                            await self.cache_manager.async_add_translation(result, detected_lang, original_text, expected_lang_code)
                            
                            # 如果配置了自动保存，则保存待处理的更改
                            if self.config.cache_auto_save:
                                await self.cache_manager.async_save_pending_changes()
                                
                            logger.info("【本地缓存更新】翻译结果已存入本地缓存")
                    except Exception as e:
                        logger.error(f"缓存更新失败: {str(e)}", exc_info=True)
                
                # 创建异步任务但不等待它完成
                asyncio.create_task(update_cache())

            if "API服务暂时不可用" in result or "503" in result: # 特殊的服务不可用错误
                logger.info("检测到服务不可用错误，执行API健康检查...")
                await self.check_api_health()
                if not self.api_health_status["healthy"]:
                    logger.warning(f"API健康检查结果: {self.api_health_status['message']}")
            
            return result if result else "翻译失败：未生成符合要求的翻译结果"
        
        except Exception as e: # 捕获translate_text_async内部的意外异常
            logger.error(f"translate_text_async 发生意外错误: {e}", exc_info=True)
            await self.replace_input_text_async(original_text)
            return f"翻译失败：发生意外错误 ({type(e).__name__})"
        finally:
            if self.config.temperature != original_temperature:
                logger.debug(f"恢复模型温度为原始值: {original_temperature}")
                self.config.temperature = original_temperature
            if hasattr(self, '_prompt_logged'):
                delattr(self, '_prompt_logged')
            if hasattr(self, '_prompt_length_logged'):
                delattr(self, '_prompt_length_logged')
            if hasattr(self, '_filter_text_logged'):
                delattr(self, '_filter_text_logged')

    async def _translate_with_api(self, prompt: str, detected_lang: str, original_text: str, expected_lang_code: str, model_to_use: str, is_retry: bool, recursive_count: int = 0) -> str:
        """使用API翻译文本
        
        Args:
            prompt (str): 提供给API的完整提示词。
            detected_lang (str): 检测到的原始文本语言代码。
            original_text (str): 用户输入的原始文本，用于特定情况下的返回。
            expected_lang_code (str): 期望翻译成的目标语言代码。
            model_to_use (str): 当前尝试使用的模型ID。
            is_retry (bool): 标记这是否是一次使用备用模型的重试。
            recursive_count (int): 用于防止因"翻译结果与原文语言相同"导致的无限递归。

        Returns:
            str: 成功时返回翻译后的文本，失败时返回以 "翻译失败：" 开头的特定错误信息。
        """
        # recursive_count 仍然用于处理"翻译结果与原文语言相同"的递归
        if recursive_count > 2: # 此限制针对"同语言"递归
            logger.error(f"翻译失败：达到最大同语言递归次数 ({recursive_count})。模型: {model_to_use}")
            return "翻译失败：出现递归翻译错误，可能是语言检测出现循环依赖"
            
        current_time = time.time()
        self.last_request_time = current_time
        
        if not hasattr(self, '_prompt_length_logged') or self._prompt_length_logged != prompt:
            logger.debug(f"【API请求】提示词长度: {len(prompt)} 字符, 模型: {model_to_use}, 是否重试: {is_retry}")
            self._prompt_length_logged = prompt
        
        current_mode = self.mode_config["translation_modes"].get(self.config.translation_mode, self.mode_config["translation_modes"][1])
        
        # "翻译结果与原文语言相同" 的检查与处理逻辑，只在非重试（即首次尝试）时，且 recursive_count 为0时执行
        # 因为重试时，我们是期望API能给出不同结果，即使语言相同也可能是API理解问题
        if not is_retry and detected_lang == expected_lang_code and recursive_count == 0:
            logger.info(f"检测到输入文本语言({detected_lang})与目标语言相同，无需翻译 (模型: {model_to_use})")
            return original_text
        
        tcp_connector_config = self.config.tcp_connector
        connector = aiohttp.TCPConnector(
            limit=tcp_connector_config.get("limit", 10),
            ssl=False, 
            ttl_dns_cache=tcp_connector_config.get("ttl_dns_cache", 300),
            keepalive_timeout=tcp_connector_config.get("keepalive_timeout", 60),
            force_close=False 
        )
        
        timeout_config = self.config.timeout
        timeout = aiohttp.ClientTimeout(
            total=timeout_config.get("total", 30),
            connect=timeout_config.get("connect", 10),
            sock_connect=timeout_config.get("sock_connect", 10),
            sock_read=timeout_config.get("sock_read", 20)
        )

        api_key = get_real_api_key(self.config.api_key)
        if not api_key:
            # 这是配置问题，重试也无用
            logger.error("API密钥未设置或解密失败，无法进行翻译")
            return "翻译失败：API密钥未设置或解密失败"

        # 记录API密钥前几个字符用于调试（不要记录完整密钥）
        safe_key_display = api_key[:5] + "..." if len(api_key) > 5 else "无效密钥"
        logger.debug(f"使用API密钥进行翻译请求: {safe_key_display}")

        headers = {"Content-Type": "application/json"}
        request_data = {}
        url = ""
        extract_fn: Callable[[Dict], str] = lambda res: ""
        check_fn: Callable[[Dict], bool] = lambda res: False

        # 定义提取函数
        def extract_gemini(result: Dict) -> str:
            if result and "candidates" in result and result["candidates"]:
                candidate = result["candidates"][0]
                finish_reason = candidate.get("finishReason", "")
                if finish_reason == "SAFETY":
                    logger.warning(f"Gemini API 内容因安全原因被过滤 (模型: {model_to_use})")
                    return "翻译失败：内容被API安全系统过滤 (Gemini)"
                if finish_reason == "RECITATION": # Google新增的finishReason
                    logger.warning(f"Gemini API 内容因背诵受限 (模型: {model_to_use})")
                    return "翻译失败：内容因API背诵限制被过滤 (Gemini)"                            
                if finish_reason == "OTHER":
                    logger.warning(f"Gemini API 因其他原因完成，可能存在问题 (模型: {model_to_use})")
                    # 可以考虑返回特定错误或空字符串，让上层重试
                    return "翻译失败：API因未知原因提前终止 (Gemini)" 
                if finish_reason == "MAX_TOKENS":
                    logger.warning(f"Gemini API 翻译结果已达到最大Token数限制 (模型: {model_to_use})，程序将尝试提取已生成的部分内容。")
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if parts and "text" in parts[0]:
                        return parts[0]["text"].strip()
            # 如果 Gemini API 返回空结果或错误结构，但没有明确的 error 对象 (例如 HTTP 200 但内容不对)
            if not (result and "candidates" in result and result["candidates"] and \
                    "content" in result["candidates"][0] and "parts" in result["candidates"][0]["content"] and \
                    result["candidates"][0]["content"]["parts"] and "text" in result["candidates"][0]["content"]["parts"][0]):
                logger.warning(f"Gemini API 返回了非预期的空/错误结构 (模型: {model_to_use}): {result}")
                return "翻译失败：API返回空或无效响应 (Gemini)" # 返回特定错误信息
            return "" # 默认返回空字符串，表示未提取到内容

        def extract_openai(result: Dict) -> str:
            if result and "choices" in result and result["choices"]:
                choice = result["choices"][0]
                finish_reason = choice.get("finish_reason", "")
                if finish_reason == "content_filter":
                    logger.warning(f"OpenAI API 内容因内容过滤被拦截 (模型: {model_to_use})")
                    return "翻译失败：内容被API内容过滤系统拦截 (OpenAI)"
                if finish_reason == "length":
                    logger.warning(f"OpenAI API 翻译结果已达到最大长度限制 (模型: {model_to_use})，程序将尝试提取已生成的部分内容。")
                if "message" in choice and "content" in choice["message"]:
                    content = choice["message"]["content"]
                    if content:
                        return content.strip()
            # 如果 OpenAI API 返回空结果或错误结构
            if not (result and "choices" in result and result["choices"] and \
                    "message" in result["choices"][0] and "content" in result["choices"][0]["message"]):
                logger.warning(f"OpenAI API 返回了非预期的空/错误结构 (模型: {model_to_use}): {result}")
                return "翻译失败：API返回空或无效响应 (OpenAI)" # 返回特定错误信息
            return "" # 默认返回空字符串，表示未提取到内容

        try:
            if self.config.api_mode == "gemini":
                import urllib.parse
                encoded_api_key = urllib.parse.quote(api_key.strip())
                url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_to_use}:generateContent?key={encoded_api_key}"
                safety_settings = self.config.safety_settings
                
                # 检查是否设置了思考预算
                thinking_config = None
                if self.config.thinking_budget_tokens > 0:
                    thinking_config = {
                        "thinkingBudget": self.config.thinking_budget_tokens
                    }
                    logger.debug(f"已启用思考模式，预算: {self.config.thinking_budget_tokens} tokens")
                else:
                    logger.debug("思考模式已禁用（未设置thinkingConfig参数 或预算为0）")
                
                generation_config = {
                    "temperature": self.config.temperature,
                    "maxOutputTokens": self.config.max_output_tokens,
                    "topP": self.config.top_p,
                    "topK": self.config.top_k
                }
                
                # 如果启用了思考模式，添加思考配置
                if thinking_config:
                    generation_config["thinkingConfig"] = thinking_config
                else:
                    # 明确禁用思考模式
                    generation_config["thinkingConfig"] = {"thinkingBudget": 0}
                    logger.debug("明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)")
                
                request_data = {
                    "contents": [{"parts": [{"text": prompt}]}],
                    "generationConfig": generation_config,
                    "safetySettings": safety_settings.get("gemini", [])
                }
                
                extract_fn = extract_gemini
                check_fn = lambda result: "candidates" in result and len(result["candidates"]) > 0
            elif self.config.api_mode == "openai":
                # OpenAI API 配置
                api_base_url = self.config.api_base_url
                api_endpoint = self.config.api_endpoint
                if not api_endpoint.startswith('/'):
                    api_endpoint = '/' + api_endpoint
                url = f"{api_base_url.rstrip('/')}{api_endpoint}"
                headers["Authorization"] = f"Bearer {api_key.strip()}"
                
                request_data = {
                    "model": model_to_use,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": self.config.temperature,
                    "max_tokens": self.config.max_output_tokens,
                    "top_p": self.config.top_p,
                    "frequency_penalty": self.config.frequency_penalty,
                    "presence_penalty": self.config.presence_penalty
                }
                
                extract_fn = extract_openai
                check_fn = lambda result: "choices" in result and len(result["choices"]) > 0
            else:
                logger.error(f"不支持的API模式: {self.config.api_mode}")
                return f"翻译失败：不支持的API模式: {self.config.api_mode}"

            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                if self.config.show_gui_progress and gui_handler is not None:
                    current_progress = 70 if is_retry else 50 # 重试时进度条可以靠后一些
                    self.root.after(0, gui_handler.update_progress_indicator, "translating", current_progress, f"调用API ({self.config.api_mode} - {model_to_use})...")
                
                # 检查是否被中止
                if not self.in_progress:
                    logger.info(f"检测到翻译被中止，主动退出翻译流程 (_translate_with_api-请求前, 模型: {model_to_use})")
                    return "翻译已中止"

                async with session.post(url, headers=headers, json=request_data) as response:
                    status_code = response.status
                    response_text = await response.text()
                    
                    # 记录API响应
                    if self.config.debug_mode:
                        # 限制响应文本长度，避免日志过大
                        log_text = response_text[:1000] + ("..." if len(response_text) > 1000 else "")
                        logger.debug(f"【API响应原始文本】模型: {model_to_use}, 状态码: {status_code}, 响应文本 (前1000字符): {log_text}")
                    
                    # 检查HTTP状态码
                    if status_code != 200:
                        if status_code == 429:
                            logger.error(f"API请求超过速率限制 (模型: {model_to_use})")
                            return f"翻译失败：API请求超过速率限制 (模型: {model_to_use})"
                        elif status_code == 401:
                            logger.error(f"API密钥无效或未授权 (模型: {model_to_use})")
                            return f"翻译失败：API密钥无效或未授权 (模型: {model_to_use})"
                        elif status_code == 403:
                            logger.error(f"API访问被拒绝 (模型: {model_to_use})")
                            return f"翻译失败：API访问被拒绝 (模型: {model_to_use})"
                        elif status_code == 404:
                            logger.error(f"API端点不存在 (模型: {model_to_use})")
                            return f"翻译失败：API端点不存在 (模型: {model_to_use})"
                        elif status_code == 503:
                            logger.error(f"API服务暂时不可用 (模型: {model_to_use})")
                            return f"翻译失败：API服务暂时不可用 (503) (模型: {model_to_use})"
                        else:
                            logger.error(f"API请求失败，状态码: {status_code} (模型: {model_to_use})")
                            return f"翻译失败：API请求失败，状态码: {status_code} (模型: {model_to_use})"
                    
                    # 解析JSON响应
                    try:
                        result_json = json.loads(response_text)
                        if self.config.debug_mode:
                            logger.debug(f"【API响应JSON对象】模型: {model_to_use}, 响应JSON: {result_json}")
                            
                            # 记录Token使用情况
                            logger.debug(f"【Token使用情况】模型: {model_to_use}")
                            
                            # Gemini API
                            if self.config.api_mode == "gemini" and "usageMetadata" in result_json:
                                usage = result_json["usageMetadata"]
                                prompt_token_count = usage.get("promptTokenCount", 0)
                                candidates_token_count = usage.get("candidatesTokenCount", 0)
                                total_token_count = usage.get("totalTokenCount", 0)
                                
                                # 检查是否有思考Token使用
                                thoughts_token_count = 0
                                if "promptTokensDetails" in usage:
                                    for detail in usage["promptTokensDetails"]:
                                        if detail.get("modality") == "THINKING":
                                            thoughts_token_count = detail.get("tokenCount", 0)
                                            break
                                
                                logger.debug(f"  - 思考Token数: {thoughts_token_count}")
                                logger.debug(f"  - 提示Token数: {prompt_token_count}")
                                logger.debug(f"  - 输出Token数: {candidates_token_count}")
                                logger.debug(f"  - 总Token数: {total_token_count}")
                                
                                # 如果使用了思考Token但思考预算设置为0，记录警告
                                if thoughts_token_count > 0 and self.config.thinking_budget_tokens == 0:
                                    logger.warning(f"警告：检测到思考Token使用({thoughts_token_count})，尽管已通过generationConfig.thinkingConfig.thinkingBudget=0尝试禁用思考模式。这可能是API行为或模型特性导致的。")
                    except json.JSONDecodeError:
                        logger.error(f"无法解析API响应为JSON，模型: {model_to_use}。响应: {response_text}")
                        return f"翻译失败：API响应格式错误 (非JSON) (模型: {model_to_use})"

                    if not check_fn(result_json): # 检查响应结构是否符合预期
                        logger.error(f"API响应结构不符合预期，模型: {model_to_use}。响应: {result_json}")
                        # Gemini的错误通常在 result_json["error"] 中
                        if self.config.api_mode == "gemini" and "error" in result_json:
                            gemini_error = result_json["error"].get("message", "未知Gemini错误")
                            return f"翻译失败：API返回错误 - {gemini_error[:100]} (Gemini, 模型: {model_to_use})"
                        return f"翻译失败：API响应结构无效 (模型: {model_to_use})"
                    
                    translated_text = extract_fn(result_json)

                    if not translated_text.strip(): # API返回了空翻译
                        # 这可能是由内容过滤、无效响应或模型未能生成文本引起的
                        # extract_fn 内部应该已经处理了安全过滤等，并返回了特定错误信息
                        # 如果 extract_fn 返回空，但不是特定错误，则认为是一般性空响应
                        if translated_text.startswith("翻译失败："): # extract_fn 返回了特定失败信息
                            logger.warning(f"{translated_text} (模型: {model_to_use})")
                            return translated_text
                        else:
                            logger.warning(f"API返回空翻译结果 (模型: {model_to_use})。响应: {result_json}")
                            return f"翻译失败：API返回空翻译结果 (模型: {model_to_use})"
                    
                    # 成功提取到翻译文本
                    # 检查翻译结果是否与原文语言和内容大幅相似 (只在首次尝试时，避免重试时再次进入此逻辑)
                    if not is_retry and self.config.api_mode == "gemini" and detected_lang == expected_lang_code: 
                        # 对于Gemini, 如果输入输出语言相同，有时它会返回几乎原文的内容
                        # 增加一个相似度检查，如果过于相似，也认为是一种需要调整的翻译问题
                        similarity_threshold_same_lang = 0.9 # 可以调整这个阈值
                        similarity = self.calculate_text_similarity(original_text, translated_text)
                        if similarity >= similarity_threshold_same_lang:
                            logger.warning(f"Gemini返回结果与原文 ({detected_lang}) 高度相似 ({similarity:.2f})，可能未翻译。将尝试调整。模型: {model_to_use}")
                            # 对于这种情况，我们不立即失败，而是允许上层的 recursive_count 机制处理（如果适用）
                            # 或者，如果 recursive_count 逻辑不再适用，则这里应该返回一个错误，触发重试
                            # 当前保留，依赖 recursive_count (如果build_prompt会修改prompt)
                            pass # 允许 recursive_count 机制（如果 applicable for same lang）

                    # 翻译成功，但仍需检查是否与原文的语言代码相同 (非内容相似，而是语言代码)
                    # 这部分递归调用现在由 recursive_count 控制
                    translated_lang_detected = self.detect_language(translated_text, is_original=False)
                    if translated_lang_detected == detected_lang and detected_lang != expected_lang_code and recursive_count < 2:
                        logger.warning(f"翻译结果 ({translated_lang_detected}) 与源语言 ({detected_lang}) 相同，但期望为 ({expected_lang_code})。模型: {model_to_use}。尝试调整提示词并重试 (第 {recursive_count + 1} 次)")
                        # 修改提示，要求更严格的语言输出
                        new_prompt = prompt + f"\n请严格确保输出语言为 {self.get_language_name(expected_lang_code)} ({expected_lang_code})，而不是 {self.get_language_name(detected_lang)}。"
                        return await self._translate_with_api(new_prompt, detected_lang, original_text, expected_lang_code, model_to_use, is_retry, recursive_count + 1)
                    
                    logger.info(f"API翻译成功。模型: {model_to_use}")
                    return translated_text

        except aiohttp.ClientConnectorError as e:
            logger.error(f"API连接错误 (模型: {model_to_use}): {e}")
            # 网络连接问题，通常不需要切换模型重试，因为可能是DNS或本地网络问题
            return f"翻译失败：网络连接错误 (模型: {model_to_use}) - {type(e).__name__}"
        except asyncio.TimeoutError: # aiohttp.ClientTimeout 通常会转换为这个
            logger.error(f"API请求超时 (模型: {model_to_use})")
            return f"翻译失败：API请求超时 (模型: {model_to_use})"
        except aiohttp.ClientError as e: # 其他 aiohttp 客户端错误
            logger.error(f"API客户端错误 (模型: {model_to_use}): {e}")
            return f"翻译失败：API客户端错误 (模型: {model_to_use}) - {type(e).__name__}"
        except Exception as e: # 其他所有意外错误
            logger.error(f"翻译过程中发生未知错误 (模型: {model_to_use}): {e}", exc_info=True)
            return f"翻译失败：发生未知API错误 (模型: {model_to_use}) - {type(e).__name__}"

    async def replacement_translation(self):
        """替换输入文本翻译功能，处理剪贴板内容"""
        try:
            self.in_progress = True
            # 检查是否被中止
            if not self.in_progress:
                logger.info("检测到翻译被中止，主动退出翻译流程（replacement_translation-开始）")
                return
            text = self.get_clipboard_text()
            if not text or len(text.strip()) == 0:
                logger.warning("剪贴板内容为空")
                self.in_progress = False
                return
            self.original_text = text
            if len(text) > self.config.max_text_length:
                logger.error("文本超出长度限制")
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "network_error", 0, "文本过长")
                    self.root.after(0, gui_handler.hide_progress_indicator)
                self.in_progress = False
                return
            logger.info(f"【原文】\n{text}")
            detected_lang = self.detect_language(text, is_original=True)
            current_mode = self.mode_config["translation_modes"].get(
                self.config.translation_mode, 
                self.mode_config["translation_modes"][1]
            )
            if detected_lang == "unknown":
                logger.warning("无法准确识别语言，尝试使用language_features进行识别")
                supported_langs = set()
                for mode in self.mode_config["translation_modes"].values():
                    if "source_code" in mode:
                        supported_langs.add(mode["source_code"])
                    if "target_code" in mode:
                        supported_langs.add(mode["target_code"])
                for lang_code, feature in self.mode_config["language_features"].items():
                    if lang_code in supported_langs and "pattern" in feature:
                        try:
                            pattern = regex.compile(feature["pattern"])
                            if pattern.search(text):
                                detected_lang = lang_code
                                logger.info(f"通过语言特征检测到语言: {detected_lang} ({feature.get('desc', '未知语言特征')})")
                                break
                        except regex.error as e:
                            logger.error(f"语言 {lang_code} 的正则表达式错误: {e}")
                if detected_lang == "unknown":
                    detected_lang = current_mode["source_code"]
                    logger.info(f"无法识别语言，使用当前模式默认源语言: {detected_lang}")
            expected_lang_code = None
            if detected_lang == current_mode["target_code"]:
                expected_lang_code = current_mode["source_code"]
                logger.info(f"检测到目标语言文本，执行反向翻译为: {expected_lang_code}")
            else:
                expected_lang_code = current_mode["target_code"]
                logger.info(f"执行正向翻译为: {expected_lang_code}")
            logger.info(f"当前模型温度: {self.config.temperature}, Top-P: {self.config.top_p}")
            prompt, prompt_lang = self.build_prompt(text, detected_lang)
            if self.config.show_gui_progress and gui_handler is not None:
                self.root.after(0, gui_handler.show_progress_indicator)
                gui_handler.update_progress_indicator("preparing", 10)
                await asyncio.sleep(0.1)
            is_network_ok = self.service_manager.is_network_connected()
            if not is_network_ok:
                logger.error("网络连接不可用")
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "network_error", 0)
                    self.root.after(0, gui_handler.hide_progress_indicator)
                await self.replace_input_text_async(self.original_text)
                self.in_progress = False
                return
            # 检查是否被中止
            if not self.in_progress:
                logger.info("检测到翻译被中止，主动退出翻译流程（replacement_translation-翻译前）")
                return
            try:
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "translating", 40)
                # 检查是否被中止
                if not self.in_progress:
                    logger.info("检测到翻译被中止，主动退出翻译流程（replacement_translation-调用翻译前）")
                    return
                translated = await self.translate_text_async(prompt, detected_lang, text, expected_lang_code)
                # 检查是否被中止
                if not self.in_progress:
                    logger.info("检测到翻译被中止，主动退出翻译流程（replacement_translation-翻译后）")
                    return
                if translated.startswith("翻译失败："):
                    if "网络连接不可用" in translated:
                        logger.error("翻译过程中网络连接不可用")
                        if self.config.show_gui_progress and gui_handler is not None:
                            self.root.after(0, gui_handler.update_progress_indicator, "network_error", 40)
                            self.root.after(0, gui_handler.hide_progress_indicator)
                        await self.replace_input_text_async(self.original_text)
                        self.in_progress = False
                        return
                    if "网络" in translated:
                        is_network_ok = self.service_manager.is_network_connected(force_check=True)
                        if not is_network_ok:
                            logger.error("网络连接不可用，无法继续翻译")
                            if self.config.show_gui_progress and gui_handler is not None:
                                self.root.after(0, gui_handler.update_progress_indicator, "network_error", 40)
                                self.root.after(2000, gui_handler.hide_progress_indicator)
                            await self.replace_input_text_async(self.original_text)
                            self.in_progress = False
                            return
                    logger.error(f"翻译失败: {translated}")
                    if self.config.show_gui_progress and gui_handler is not None:
                        self.root.after(0, gui_handler.update_progress_indicator, "api_error", 60)
                        self.root.after(0, gui_handler.hide_progress_indicator)
                    await self.replace_input_text_async(self.original_text)
                    self.in_progress = False
                    return
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "translating", 80, "准备替换文本...")
                logger.info(f"【翻译结果】\n{translated}")
                
                # 先替换输入框文本
                await self.replace_input_text_async(translated)
                
                # 再更新历史记录
                if self.config.translation_mode not in self.history:
                    self.history[self.config.translation_mode] = []
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.history[self.config.translation_mode].append({
                    "time": current_time,
                    "source": text,
                    "source_lang": detected_lang,
                    "translation": translated,
                    "target_lang": expected_lang_code
                })
                # 新增：记录上一次成功翻译的目标语言代码
                self.last_translation_target_code = expected_lang_code
                
                # 记录日志
                logger.debug(f"记录上次翻译目标语言: {expected_lang_code}")
                
                # 隐藏进度指示器
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.hide_progress_indicator)
                
                logger.info("翻译完成，结果已替换输入框内容")
            except Exception as e:
                logger.error(f"翻译过程中发生错误: {e}", exc_info=True)
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "api_error", 0)
                    self.root.after(0, gui_handler.hide_progress_indicator)
                await self.replace_input_text_async(self.original_text)
            finally:
                self.in_progress = False
        except Exception as e:
            logger.error(f"替换翻译功能发生错误: {e}", exc_info=True)
            self.in_progress = False

    def clear_all_context(self):
        """清空所有模式的上下文和内存缓存，但保留本地缓存"""
        for mode in self.history:
            self.history[mode].clear()
        logger.info("所有翻译模式的上下文已清空")
        
        # 清空内存翻译缓存
        if hasattr(self, 'translation_cache'):
            self.translation_cache.clear()
            logger.info(f"内存翻译缓存已清空")
        
        # 提示用户本地缓存未清空
        if hasattr(self, 'cache_manager') and self.config.use_local_cache:
            logger.info("注意：本地翻译缓存未清空，如需清空，请在设置菜单中操作")
        
        # 清空上下文后，进行一次垃圾回收
        logger.debug("执行垃圾回收 (清空上下文和内存缓存后)")
        gc.collect()

    # 添加create_main_window方法
    def create_main_window(self):
        """创建主窗口相关设置"""
        logger.debug("初始化翻译器主窗口设置")
        
        # 初始化实例变量
        self._in_progress = False  # 译文替换进行中标志
        self._suppress_keyboard = False  # 键盘事件抑制标志
        
        # 设置窗口属性
        if self.root:
            self.root.title("翻译助手")
            self.root.withdraw()  # 隐藏主窗口，只在任务栏显示图标
            
            # 防止root窗口被误关闭，改为调用translator的关闭方法
            self.root.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 初始化GUI状态变量
        self.gui_variables = {
            "status": tk.StringVar(value="就绪"),
            "current_mode": tk.StringVar(value=str(self.config.translation_mode))
        }
        
        logger.debug("主窗口设置完成")

    def on_close(self):
        """处理窗口关闭事件"""
        # 保存待处理的本地缓存
        if hasattr(self, 'cache_manager'):
            logger.info("正在保存本地缓存...")
            self.cache_manager.save_pending_changes()
            self.cache_manager.close()
            logger.info("本地缓存已保存")
            
        self.shutdown()
        if self.root:
            self.root.destroy()

    def setup_keyboard_listener(self):
        """设置键盘监听器"""
        logger.debug("设置键盘监听器")
        # 键盘监听器在主函数中创建，此处不需要再创建

    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两段文本的相似度
        
        Args:
            text1: 第一段文本
            text2: 第二段文本
            
        Returns:
            相似度 (0.0-1.0)
        """
        # 提取支持的语言代码，用于作为占位符
        # 实际计算不需要具体语言代码，但函数需要此参数
        supported_lang_codes = set()
        for mode_data in self.mode_config.get("translation_modes", {}).values():
            if "source_code" in mode_data:
                source_code = mode_data["source_code"]
                supported_lang_codes.add(source_code)
                break
                
        # 如果没有找到语言代码，使用默认值
        if not supported_lang_codes:
            source_code = "en"
        else:
            source_code = next(iter(supported_lang_codes))
            
        # 使用language_utils函数的assess_quality模式内部的文本相似度计算逻辑
        # 但返回计算得到的相似度而非质量评估结果
        from difflib import SequenceMatcher
        
        def clean_text(text):
            # 统一空白字符
            text = regex.sub(r'\s+', ' ', text).strip()
            # 去除常见标点符号
            text = regex.sub(r'[\p{P}\p{S}]', '', text)
            # 统一大小写（如果是拉丁字母）
            text = text.lower()
            return text
            
        # 清理文本
        clean_text1 = clean_text(text1)
        clean_text2 = clean_text(text2)
        
        # 对于较短的文本，直接使用SequenceMatcher
        if len(clean_text1) < 100 and len(clean_text2) < 100:
            return SequenceMatcher(None, clean_text1, clean_text2).ratio()
            
        # 对于较长文本，使用n-gram方法
        def get_ngrams(text, n=3):
            return set(text[i:i+n] for i in range(max(0, len(text) - n + 1)))
            
        # 计算3-gram和2-gram的Jaccard相似度
        ngrams1 = get_ngrams(clean_text1, 3)
        ngrams2 = get_ngrams(clean_text2, 3)
        
        if not ngrams1 or not ngrams2:
            # 文本太短，改用2-gram
            ngrams1 = get_ngrams(clean_text1, 2)
            ngrams2 = get_ngrams(clean_text2, 2)
            
        # 计算Jaccard相似度
        intersection = len(ngrams1.intersection(ngrams2))
        union = len(ngrams1.union(ngrams2))
        
        return intersection / union if union > 0 else 0.0

    def get_language_name(self, lang_code: str) -> str:
        """获取语言代码对应的语言名称
        
        Args:
            lang_code: ISO 639-1语言代码
            
        Returns:
            str: 语言名称，如果找不到对应的语言名称则返回原始代码
        """
        try:
            # 从配置中获取语言映射
            language_map = self.config.get('language_map', {})
            # 返回映射的语言名称，如果不存在则返回原始代码
            return language_map.get(lang_code, lang_code)
        except Exception as e:
            logger.error(f"获取语言名称时出错: {e}")
            return lang_code

    def translate_text(self, text: str) -> str:
        """翻译文本
        
        Args:
            text: 要翻译的文本
            
        Returns:
            str: 翻译后的文本
        """
        try:
            # 保存原文，用于失败时恢复
            self.original_text = text
            
            # ... existing translation code ...
            
        except Exception as e:
            logger.error(f"翻译过程中出现未知错误: {e}")
            # 确保恢复原文
            self.restore_original_text()
            raise
            
    def restore_original_text(self) -> None:
        """恢复原始文本到输入框"""
        try:
            if hasattr(self, 'original_text'):
                # 使用replace_input_text方法而非直接操作GUI
                self.replace_input_text(self.original_text)
                logger.info("已恢复原文到输入框")
        except Exception as e:
            logger.error(f"恢复原文时出错: {e}")

    # 翻译取消功能已移除

    def get_clipboard_text(self) -> Optional[str]:
        """获取剪贴板文本"""
        for attempt in range(5):
            try:
                text = pyperclip.paste()
                if text:
                    return text
            except Exception as e:
                logger.warning(f"剪贴板读取失败，第{attempt+1}次尝试: {e}")
            time.sleep(0.2)
        logger.error("无法获取剪贴板内容")
        return None

    def replace_input_text(self, text: str):
        """替换输入框文本"""
        with self.replace_lock:
            current_time = time.time()
            if current_time - self.last_replace_time < 0.5:
                logger.debug("替换操作过于频繁，跳过")
                return
            self.suppress_keyboard = True
            try:
                pyperclip.copy(text)
                # 减少等待时间
                time.sleep(0.05)
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.02)
                pyautogui.press('delete')
                time.sleep(0.02)
                pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.05)
                self.last_replace_time = current_time
                logger.debug(f"输入框内容已替换为: {text}")
            except Exception as e:
                logger.error(f"替换输入框内容失败: {e}")
            finally:
                self.suppress_keyboard = False

    async def replace_input_text_async(self, text: str):
        """异步替换输入框文本"""
        # 使用run_in_executor代替to_thread以减少开销
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, self.replace_input_text, text)

class GUIHandler:
    def __init__(self, root: Optional[tk.Tk]):
        self.root = root
        # 不再在初始化时隐藏主窗口，避免影响mainloop
        self.progress_indicator: Optional[tk.Toplevel] = None
        self.progress_label: Optional[tk.Label] = None
        self.progress_bar: Optional[ttk.Progressbar] = None
        self.status_label: Optional[tk.Label] = None
        self.progress_after_id: Optional[str] = None
        self.is_showing: bool = False
        self.dots_count = 0
        self.dots_animation_id = None
        self.current_progress = 0
        self.target_progress = 0
        self.animation_speed = 10  # 进度条动画速度
        self.animation_id = None
        
        # 设置主题颜色
        self.theme = {
            "background": "#f0f0f5",        # 背景色
            "text": "#333333",              # 主文本色
            "secondary_text": "#666666",    # 次要文本色
            "accent": "#4a86e8",            # 强调色（进度条）
            "success": "#2ecc71",           # 成功色
            "error": "#e74c3c",             # 错误色
            "border": "#dddddd"             # 边框色
        }
        
        # 配置进度条样式
        self._setup_styles()
        
        # 简化为只使用中文文本提示
        self.progress_text = "翻译中"
        
        # 简化为中文状态提示
        self.status_texts = {
            "network_error": "网络连接不可用",
            "api_error": "API服务不可用",
            "success": "翻译完成",
            "preparing": "准备翻译",
            "translating": "正在翻译"
        }

    def _setup_styles(self):
        """设置自定义样式"""
        if self.root:
            style = ttk.Style()
            # 创建自定义进度条样式
            style.configure(
                "Slim.Horizontal.TProgressbar",
                troughcolor=self.theme["border"],
                background=self.theme["accent"],
                thickness=4,
                borderwidth=0,
                relief="flat"
            )
            
    def _apply_window_styling(self, window):
        """应用窗口样式"""
        if window:
            try:
                # 设置窗口背景色
                window.configure(background=self.theme["background"])
                
                # 设置完全不透明
                window.attributes("-alpha", 1.0)
                
                # 设置置顶
                window.attributes("-topmost", True)
                
                # 如果是Windows平台，设置窗口为工具窗口并确保不获取焦点
                if sys.platform == "win32":
                    try:
                        # 设置为工具窗口
                        window.attributes("-toolwindow", 1)
                        
                        # 使用Windows API进一步确保窗口不获取焦点
                        hwnd = window.winfo_id()
                        
                        # SetWindowPos标志
                        SWP_NOACTIVATE = 0x0010
                        SWP_NOMOVE = 0x0002
                        SWP_NOSIZE = 0x0001
                        
                        # 将窗口置于顶层但不激活
                        ctypes.windll.user32.SetWindowPos(
                            hwnd, -1, 0, 0, 0, 0, 
                            SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE
                        )
                        
                        # 设置窗口样式为无激活
                        GWL_EXSTYLE = -20
                        WS_EX_NOACTIVATE = 0x08000000
                        WS_EX_TOOLWINDOW = 0x00000080
                        
                        style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                        ctypes.windll.user32.SetWindowLongW(
                            hwnd, GWL_EXSTYLE, 
                            style | WS_EX_NOACTIVATE | WS_EX_TOOLWINDOW
                        )
                    except Exception as e:
                        logger.debug(f"设置窗口不获取焦点失败: {e}")
            except Exception as e:
                logger.error(f"应用窗口样式失败: {e}")

    def show_progress_indicator(self):
        """显示进度提示"""
        if self.is_showing:
            logger.debug("GUI已在显示中")
            return
            
        try:
            # 创建新窗口
            self.progress_indicator = tk.Toplevel(self.root)
            self.progress_indicator.title("")
            
            # 移除标题栏和边框，使其成为纯提示窗口
            self.progress_indicator.overrideredirect(True)
            
            # 应用窗口样式
            self._apply_window_styling(self.progress_indicator)
            
            # 设置窗口大小和位置
            window_width = 170
            window_height = 26
            
            # 获取鼠标当前位置
            mouse_x, mouse_y = pyautogui.position()
            
            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # 计算窗口位置，默认显示在鼠标右下方
            x = mouse_x + 10  # 修改为与_update_position相同的偏移量
            y = mouse_y + 5   # 修改为与_update_position相同的偏移量
            
            # 如果窗口超出屏幕右边界，则显示在鼠标左侧
            if x + window_width > screen_width:
                x = mouse_x - window_width - 10  # 修改为与_update_position相同的偏移量
            
            # 如果窗口超出屏幕下边界，则显示在鼠标上方
            if y + window_height > screen_height:
                y = mouse_y - window_height - 5   # 修改为与_update_position相同的偏移量
            
            # 确保窗口完全在屏幕内
            x = max(0, min(x, screen_width - window_width))
            y = max(0, min(y, screen_height - window_height))
            
            # 设置窗口位置和大小
            self.progress_indicator.geometry(f"{window_width}x{window_height}+{x}+{y}")
                
            # 创建主框架 - 添加细边框和圆角效果
            main_frame = tk.Frame(
                self.progress_indicator, 
                bg=self.theme["background"], 
                bd=0,  # 去除内部边框
                relief=tk.FLAT,
                highlightbackground=self.theme["border"],
                highlightthickness=1
            )
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 创建单行布局框架，使用紧凑布局
            row_frame = tk.Frame(main_frame, bg=self.theme["background"])
            row_frame.pack(fill=tk.BOTH, expand=True)
            
            # 直接使用中文提示文本
            self.progress_label = tk.Label(
                row_frame, 
                text=self.progress_text, 
                bg=self.theme["background"], 
                fg=self.theme["text"],
                font=("微软雅黑", 9),
                padx=5,  # 使用padding代替固定宽度
                anchor="center"  # 居中对齐
            )
            self.progress_label.pack(side=tk.LEFT)
            
            # 进度条 - 调整比例
            self.progress_bar = ttk.Progressbar(
                row_frame, 
                orient="horizontal",
                length=100,  # 长一些，占据主要空间
                mode="determinate",
                style="Slim.Horizontal.TProgressbar"
            )
            self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 5), pady=9)  # 垂直居中
            
            # 隐藏状态标签，简化界面
            self.status_label = tk.Label(
                row_frame,
                text="",
                bg=self.theme["background"],
                fg=self.theme["secondary_text"],
                font=("微软雅黑", 8)
            )
            # 不显示状态标签，使界面更简洁
            # self.status_label.pack(side=tk.LEFT, padx=(0, 5))
            
            # 初始化进度为0
            self.progress_bar["value"] = 0
            self.current_progress = 0
            
            # 设置默认目标进度为10%
            self._animate_progress_to(10)
            
            # 现在显示窗口
            self.progress_indicator.deiconify()
            
            # 设置定时器更新位置和动画
            # 确保之前的定时器已被取消
            if self.progress_after_id and self.root:
                try:
                    self.root.after_cancel(self.progress_after_id)
                except:
                    pass
            # 启动新的定时器循环
            self.progress_after_id = self.root.after(200, self._update_position)
            
            # 开始点动画
            self._start_dots_animation()
            
            # 设置标志
            self.is_showing = True
            
            logger.debug("显示进度指示器")
        except Exception as e:
            logger.error(f"显示进度指示器失败: {e}", exc_info=True)

    def _cleanup_resources(self):
        """清理所有资源和定时器"""
        # 清理动画计时器
        if self.dots_animation_id and self.root:
            try:
                self.root.after_cancel(self.dots_animation_id)
            except:
                pass
        self.dots_animation_id = None
            
        # 清理进度条动画
        if self.animation_id and self.root:
            try:
                self.root.after_cancel(self.animation_id)
            except:
                pass
        self.animation_id = None
            
        # 清理位置更新定时器
        if self.progress_after_id and self.root:
            try:
                self.root.after_cancel(self.progress_after_id)
            except:
                pass
        self.progress_after_id = None
            
        # 销毁窗口
        if self.progress_indicator is not None:
            try:
                # 检查窗口是否仍然有效
                if hasattr(self.progress_indicator, 'winfo_exists') and self.progress_indicator.winfo_exists():
                    self.progress_indicator.destroy()
                else:
                    logger.debug("进度提示窗口已不存在，无需销毁")
            except Exception as e:
                logger.error(f"销毁进度提示窗口时出错: {e}")
            finally:
                self.progress_indicator = None
                
        # 重置所有引用
        self.progress_indicator = None
        self.progress_label = None
        self.progress_bar = None
        self.status_label = None
        self.dots_count = 0

    def _animate_progress_to(self, target_percent):
        """平滑动画过渡到目标进度百分比"""
        self.target_progress = target_percent
        
        if self.animation_id:
            try:
                if self.root:
                    self.root.after_cancel(self.animation_id)
            except:
                pass
                
        def _update_progress_animation():
            if not self.is_showing or not self.progress_bar or not self.progress_indicator:
                return
                
            try:
                if self.current_progress < self.target_progress:
                    # 逐步增加进度
                    step = max(0.5, (self.target_progress - self.current_progress) / 10)
                    self.current_progress = min(self.current_progress + step, self.target_progress)
                    if self.progress_bar:
                        self.progress_bar["value"] = self.current_progress
                    
                    # 继续动画直到达到目标
                    if self.current_progress < self.target_progress and self.root:
                        self.animation_id = self.root.after(self.animation_speed, _update_progress_animation)
            except Exception as e:
                logger.error(f"进度动画更新错误: {e}")
                
        # 启动动画
        _update_progress_animation()

    def _reset_progress_animation(self):
        """重置进度条动画"""
        self.current_progress = 0
        self.target_progress = 0
        if self.progress_bar:
            self.progress_bar["value"] = 0
        self._animate_progress_to(10)  # 从0开始，先到10%

    def _start_dots_animation(self):
        """启动点点动画"""
        if self.dots_animation_id:
            try:
                if self.root:
                    self.root.after_cancel(self.dots_animation_id)
            except:
                pass
                
        def _animate_dots():
            try:
                if not self.is_showing or not self.progress_label:
                    return
                
                if not self.progress_label.winfo_exists():
                    return
                    
                # 获取当前文本和基础文本
                default_lang = "zh"
                base_text = self.progress_texts.get(default_lang, "翻译中")
                
                # 更紧凑的点动画方式，为水平布局优化
                dots = ["", ".", "..", "..."]
                self.dots_count = (self.dots_count + 1) % 4
                
                # 为单行布局显示文本
                new_text = f"{base_text}{dots[self.dots_count]}"
                
                # 只有当文本变化时才更新
                current_text = self.progress_label["text"]
                if current_text != new_text:
                    self.progress_label.config(text=new_text)
                
                # 使用更短的动画间隔使动画更流畅
                if self.root and self.is_showing:
                    self.dots_animation_id = self.root.after(300, _animate_dots)
            except Exception as e:
                logger.error(f"点动画更新错误: {e}")
                
        # 启动动画
        _animate_dots()

    def _update_position(self, _is_direct_call=False):
        """更新进度提示位置"""
        try:
            # 检查root和progress_indicator是否有效
            if (self.root is None or not self.root.winfo_exists() or 
                self.progress_indicator is None):
                logger.debug("无法更新位置：窗口已无效")
                if not _is_direct_call:
                    self.progress_after_id = None
                return
                
            # 检查progress_indicator是否仍然存在
            try:
                if not self.progress_indicator.winfo_exists():
                    logger.debug("无法更新位置：提示窗口已关闭")
                    if not _is_direct_call:
                        self.progress_after_id = None
                    return
                    
                # 更新位置，使其跟随鼠标但有一定偏移量，避免遮挡
                new_x, new_y = pyautogui.position()
                window_width = self.progress_indicator.winfo_width()
                window_height = self.progress_indicator.winfo_height()
                
                # 获取屏幕尺寸
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                
                # 优化位置逻辑 - 根据鼠标位置放置窗口在不同位置
                offset_x = 10  # 水平偏移量
                offset_y = 5  # 垂直偏移量
                
                # 默认在右下方显示
                pos_x = new_x + offset_x
                pos_y = new_y + offset_y
                
                # 如果靠近屏幕右边缘，则改为左侧显示
                if pos_x + window_width > screen_width:
                    pos_x = new_x - window_width - offset_x
                
                # 如果靠近屏幕下边缘，则改为上方显示
                if pos_y + window_height > screen_height:
                    pos_y = new_y - window_height - offset_y
                
                # 确保窗口始终在屏幕内
                pos_x = max(0, min(screen_width - window_width, pos_x))
                pos_y = max(0, min(screen_height - window_height, pos_y))
                
                self.progress_indicator.geometry(f"+{pos_x}+{pos_y}")
                
                # 安排下一次更新，但仅当不是直接调用且窗口仍在显示时
                if not _is_direct_call and self.is_showing:
                    self.progress_after_id = self.root.after(200, self._update_position)
            except tk.TclError as e:
                logger.debug(f"更新位置时发生Tcl错误: {e}")
                if not _is_direct_call:
                    self.progress_after_id = None
                # 如果发生Tcl错误，可能是窗口已被销毁，重置状态
                self.is_showing = False
        except Exception as e:
            logger.error(f"更新GUI位置失败: {e}")
            # 确保出错时不会继续尝试更新
            if not _is_direct_call:
                self.progress_after_id = None

    def hide_progress_indicator(self):
        """隐藏进度提示"""
        try:
            # 停止所有动画
            self._reset_progress_animation()
            
            # 先清理定时器和动画
            self._cleanup_animations_and_timers()
            
            # 销毁窗口
            if self.progress_indicator is not None:
                try:
                    # 检查窗口是否仍然有效
                    if hasattr(self.progress_indicator, 'winfo_exists') and self.progress_indicator.winfo_exists():
                        self.progress_indicator.destroy()
                    else:
                        logger.debug("进度提示窗口已不存在，无需销毁")
                except Exception as e:
                    logger.error(f"销毁进度提示窗口时出错: {e}")
                finally:
                    self.progress_indicator = None
                    
            # 重置状态
            self.is_showing = False
            logger.debug("隐藏GUI等待提示")
        except Exception as e:
            logger.error(f"隐藏进度提示时出错: {e}")
            # 确保状态被重置，即使出现错误
            self.is_showing = False
            self.progress_indicator = None

    def _cleanup_animations_and_timers(self):
        """清理动画和计时器，但不销毁窗口"""
        try:
            # 停止动画和计时器
            if self.dots_animation_id is not None and self.root:
                try:
                    self.root.after_cancel(self.dots_animation_id)
                    self.dots_animation_id = None
                except Exception as e:
                    logger.debug(f"取消 dots 动画时出错: {e}")
                    self.dots_animation_id = None  # 确保即使出错也重置ID
                
            if self.animation_id is not None and self.root:
                try:
                    self.root.after_cancel(self.animation_id)
                    self.animation_id = None
                except Exception as e:
                    logger.debug(f"取消进度动画时出错: {e}")
                    self.animation_id = None
                
            if self.progress_after_id is not None and self.root:
                try:
                    self.root.after_cancel(self.progress_after_id)
                    self.progress_after_id = None
                except Exception as e:
                    logger.debug(f"取消 progress_after 计时器时出错: {e}")
                    self.progress_after_id = None
                
            # 重置动画状态
            self.dots_count = 0
            self.current_progress = 0
            self.target_progress = 0
        except Exception as e:
            logger.error(f"清理动画和计时器时出错: {e}")

    def update_progress_indicator(self, status_key: str, progress: int = None, custom_message: str = None):
        """更新进度提示状态
        
        Args:
            status_key: 状态键，对应status_texts中的键
            progress: 进度百分比，范围0-100
            custom_message: 自定义状态消息，如果提供则优先使用
        """
        if not self.is_showing or not self.progress_indicator:
            logger.debug("进度指示器未显示，无法更新状态")
            return
            
        try:
            # 更新状态文本
            status_text = custom_message or self.status_texts.get(status_key, "")
            if self.status_label and status_text:
                self.status_label.configure(text=status_text)
                
            # 更新翻译文本，根据状态选择合适的文本
            if status_key == "translating":
                self.progress_label.configure(text=self.progress_text)
            elif status_key == "success":
                self.progress_label.configure(text=self.status_texts["success"])
            elif status_key == "network_error" or status_key == "api_error":
                self.progress_label.configure(text=self.status_texts[status_key])
                
            # 更新进度
            if progress is not None:
                self._animate_progress_to(progress)
                
            # 刷新窗口位置，指定为直接调用以避免创建新的定时器
            self._update_position(_is_direct_call=True)
        except Exception as e:
            logger.error(f"更新进度指示器失败: {e}")
            # 在出现错误时尝试隐藏指示器，避免界面卡住
            try:
                self.hide_progress_indicator()
            except:
                pass

class KeyboardListener:
    def __init__(self, translator: Translator):
        self.translator = translator
        self.listener = keyboard.Listener(on_press=self.on_press, on_release=self.on_release)
        self.listener.daemon = True
        self.space_count = 0
        self.last_space_time = 0
        self.last_trigger_time = 0
        self.space_detection_timeout = 1.0  # 空格按键超过这个时间间隔重置计数
        self.trigger_cooldown = 2.0  # 两次触发之间的最小时间间隔
        
    def get_mouse_position(self):
        """获取鼠标位置
        
        Returns:
            tuple: 鼠标坐标(x, y)，如果获取失败则返回None
        """
        try:
            import pyautogui
            return pyautogui.position()
        except Exception as e:
            logger.error(f"获取鼠标位置失败: {e}")
            return None

    def check_cancel_shortcut(self, key):
        """检查是否按下了取消翻译的快捷键
        
        Args:
            key: 按下的键
            
        Returns:
            bool: 总是返回False，因为已移除取消功能
        """
        return False

    def on_press(self, key):
        """处理键盘按键事件"""
        try:
            # 如果翻译器不可用，忽略键盘事件
            if not self.translator or not hasattr(self.translator, 'config'):
                return
            # 如果翻译器正在进行网络请求，且配置为忽略键盘事件，则拦截
            if getattr(self.translator, 'suppress_keyboard', False):
                return 

            # 只处理空格键
            if key != keyboard.Key.space:
                self.space_count = 0
                return
            # 获取鼠标坐标
            mouse_pos = self.get_mouse_position()
            # 如果获取鼠标位置失败或者鼠标在屏幕顶部区域，忽略触发
            if mouse_pos is None or mouse_pos[1] < 50:
                return
            # 计算空格按键时间间隔
            current_time = time.time()
            space_interval = current_time - self.last_space_time
            # 如果间隔过长，重置计数
            if space_interval > self.space_detection_timeout:
                self.space_count = 1
            else:
                self.space_count += 1
            self.last_space_time = current_time
            # 检查冷却时间，防止频繁触发
            if current_time - self.last_trigger_time < self.trigger_cooldown:
                return
            # 检测到三次空格，触发翻译
            if self.space_count >= 3:
                self.space_count = 0
                self.last_trigger_time = current_time
                # 检查翻译器状态
                if self.translator.config.translation_mode == 0:
                    logger.warning("请先选择翻译模式")
                    return
                if self.translator.in_progress:
                    logger.info("已有翻译任务进行中，请稍候...")
                    return
                logger.info("检测到三次空格，触发翻译")
                asyncio.run_coroutine_threadsafe(self.translator.replacement_translation(), self.translator.loop)
        except Exception as e:
            logger.error(f"键盘事件处理错误: {e}")

    def on_release(self, key):
        """处理键盘释放事件"""
        pass  # 不需要处理键盘释放事件
    
    def start(self):
        """启动键盘监听"""
        self.listener.start()

# 定义全局的设置菜单项
SETTINGS_MENU_ITEMS = {
    1: {"desc": "修改API密钥", "func": lambda t: modify_api_key(t)},
    2: {"desc": "修改模型ID", "func": lambda t: modify_model_id(t)},
    3: {"desc": "修改API模式（1: 谷歌API, 2: OpenAI兼容API）", "func": lambda t: modify_api_mode(t)},
    4: {"desc": "修改API Base URL", "func": lambda t: modify_api_base_url(t)},
    5: {"desc": "修改API Endpoint", "func": lambda t: modify_api_endpoint(t)},
    6: {"desc": "修改模型温度（建议0-2）", "func": lambda t: modify_temperature(t)},
    7: {"desc": "修改Top-P核采样（建议0-1）", "func": lambda t: modify_top_p(t)},
    8: {"desc": "修改默认翻译模式", "func": lambda t: modify_translation_mode(t)},
    9: {"desc": "添加自定义翻译模式", "func": lambda t: add_custom_translation_mode(t)},
    10: {"desc": "删除翻译模式", "func": lambda t: delete_translation_mode(t)},
    11: {"desc": "修改最大翻译文本字数", "func": lambda t: modify_max_text_length(t)},
    12: {"desc": "修改最大上下文数量（建议0-20）", "func": lambda t: modify_context_max_count(t)},
    13: {"desc": "开启/关闭调试模式", "func": lambda t: toggle_debug_mode(t)},
    14: {"desc": "修改请求最小间隔", "func": lambda t: modify_request_min_interval(t)},
    15: {"desc": "修改日志最大条目数", "func": lambda t: modify_log_max_entries(t)},
    16: {"desc": "开启/关闭GUI等待提示", "func": lambda t: toggle_show_gui_progress(t)},
    17: {"desc": "修改语言族配置", "func": lambda t: modify_language_families(t)},
    18: {"desc": "修改语言检测缓存大小", "func": lambda t: modify_language_cache_size(t)},
    19: {"desc": "修改翻译结果相似度阈值", "func": lambda t: modify_same_language_threshold(t)},
    20: {"desc": "修改思考预算 (Tokens)", "func": lambda t: modify_thinking_budget(t)},
    21: {"desc": "缓存管理设置", "func": lambda t: modify_cache_settings(t)}  # 添加缓存管理设置菜单项
}

def validate_input(prompt: str, valid_options: list = [], type_cast: Callable = str, range_check: tuple = (), allow_empty: bool = False) -> Any:
    """验证用户输入"""
    while True:
        value = input(prompt).strip()
        if value.lower() == "返回":
            return "返回"
        if not value and not allow_empty:
            print("输入不能为空，请重新输入。")
            continue
        try:
            casted_value = type_cast(value)
            if valid_options and casted_value not in valid_options:
                print(f"无效输入，请输入以下选项之一：{valid_options}")
                continue
            if range_check and (casted_value < range_check[0] or casted_value > range_check[1]):
                print(f"输入超出范围，请输入 {range_check[0]} 到 {range_check[1]} 之间的值。")
                continue
            return casted_value
        except ValueError:
            print(f"请输入有效的 {type_cast.__name__} 类型值。")
            continue

def display_menu(menu_items: Dict, title: str, current_mode: int = -1) -> None:
    """显示主菜单"""
    print(f"\n{title}：")
    translation_modes = {k: v for k, v in menu_items.items() if k not in [0, "00"]}
    settings_item = menu_items.get(0, {"desc": "设置"})
    clear_context_item = menu_items.get("00", {"desc": "清空上下文"})

    for key, item in sorted(translation_modes.items(), key=lambda x: int(x[0])):
        mark = "[√]" if key == current_mode else ""
        print(f"  {key}. {item['desc']} {mark}")

    print(f"  0. {settings_item['desc']}")
    print(f"  00. {clear_context_item['desc']}")

def display_settings_menu(menu_items: Dict, title: str, config: Config, translator: Translator) -> None:
    """显示设置菜单"""
    print(f"\n{title}：")
    for key, item in menu_items.items():
        extra_info = ""
        if key == 1:
            extra_info = f"（当前：{'已设置' if config.api_key else '未设置'}）"
        elif key == 2:
            extra_info = f"（当前：{config.model_id}）"
        elif key == 3:
            extra_info = f"（当前：{config.api_mode}）"
        elif key == 4:
            extra_info = f"（当前：{config.api_base_url}）"
        elif key == 5:
            extra_info = f"（当前：{config.api_endpoint}）"
        elif key == 6:
            extra_info = f"（当前：{config.temperature}）"
        elif key == 7:
            extra_info = f"（当前：{config.top_p}）"
        elif key == 8:
            mode_map = {k: v["desc"] for k, v in translator.mode_menu_items.items() if k not in [0, "00"]}
            extra_info = f"（当前：{mode_map.get(config.translation_mode, '未知')}）"
        elif key == 11:
            extra_info = f"（当前：{config.max_text_length}）"
        elif key == 12:
            extra_info = f"（当前：{config.context_max_count}）"
        elif key == 13:
            extra_info = f"（当前：{'开启' if config.debug_mode else '关闭'}）"
        elif key == 14:
            extra_info = f"（当前：{config.request_min_interval} 秒）"
        elif key == 15:
            extra_info = f"（当前：INFO {config.log_info_max}，其他 {config.log_other_max}）"
        elif key == 16:
            extra_info = f"（当前：{'开启' if config.show_gui_progress else '关闭'}）"
        elif key == 17:
            # 语言族配置显示
            language_families = getattr(config, 'language_families', None) or {}
            family_count = len(language_families)
            if family_count == 0:
                extra_info = "（当前：未配置）"
            else:
                extra_info = f"（当前：已配置{family_count}个语言族）"
        elif key == 18:
            # 语言检测缓存大小显示
            cache_size = getattr(config, 'language_detection_cache_size', 100)
            extra_info = f"（当前：{cache_size} 条）"
        elif key == 19:
            # 翻译结果相似度阈值显示
            threshold = getattr(config, 'same_language_match_threshold', 0.5)
            extra_info = f"（当前：{threshold:.2f}）"
        elif key == 20: # 更新思考模式显示为思考预算
            budget = getattr(config, 'thinking_budget_tokens', 0)
            extra_info = f"（当前：{budget} Tokens {' - 思考已关闭' if budget == 0 else ''}）"
        elif key == 21:
            # 缓存设置显示
            use_local = getattr(config, 'use_local_cache', True)
            priority = getattr(config, 'cache_priority', True)
            extra_info = f"（当前：{'启用' if use_local else '禁用'}本地缓存，{'优先缓存' if priority else '优先大模型'}）"
        print(f"  {key}. {item['desc']} {extra_info}")
    print("请选择（输入数字或'返回'返回主菜单）：")

def modify_api_key(translator: Translator) -> None:
    """修改API密钥配置"""
    config = translator.config
    extra_info = f"（当前：{'已设置' if config.api_key else '未设置'}）"
    print(f"修改API密钥 {extra_info}")
    print("注意：本程序只接受加密后的API密钥，请使用api_crypto.py工具进行加密。")
    print("请输入新的加密后的API密钥（直接回车保持不变）：")
    
    global api_crypto, decrypted_api_key
    new_value = input().strip()
    if not new_value:
        print("保持原值不变")
        return
    
    # 初始化API加密工具
    if not api_crypto:
        init_api_crypto()
    
    # 检查是否是加密后的API密钥
    if not api_crypto.is_encrypted(new_value):
        logger.error("输入的不是有效的加密API密钥，请使用api_crypto.py工具进行加密。")
        return
    
    # 解密测试API密钥是否有效
    decrypted_key = api_crypto.decrypt(new_value)
    if not decrypted_key:
        logger.error("加密API密钥无效或无法解密")
        return
    
    # 更新全局解密后的API密钥
    decrypted_api_key = decrypted_key
    
    # 保存加密后的API密钥到配置文件
    config.api_key = new_value
    save_config(config)
    logger.info(f"API密钥已更新（已加密并保存到配置文件）")

def modify_model_id(translator: Translator) -> None:
    """修改模型ID"""
    print("\n设置 > 修改模型ID：")
    print(f"当前值：{translator.config.model_id}")
    new_value = validate_input("请输入新的模型ID（输入'返回'以取消）：")
    if new_value != "返回":
        translator.config.model_id = new_value
        save_config(translator.config)
        logger.info(f"模型ID已更新为：{new_value}")
    else:
        print("操作取消，返回设置菜单。")

def modify_api_mode(translator: Translator) -> None:
    """修改API模式"""
    print("\n设置 > 修改API模式：")
    print(f"当前值：{translator.config.api_mode}")
    new_value = validate_input("请选择API模式（1: 谷歌API, 2: OpenAI兼容API，输入'返回'以取消）：", valid_options=[1, 2], type_cast=int)
    if new_value != "返回":
        translator.config.api_mode = "gemini" if new_value == 1 else "openai"
        save_config(translator.config)
        logger.info(f"API模式已更新为：{'谷歌API (gemini)' if new_value == 1 else 'OpenAI兼容API (openai)'}")
    else:
        print("操作取消，返回设置菜单。")

def modify_api_base_url(translator: Translator) -> None:
    """修改API Base URL"""
    print("\n设置 > 修改API Base URL：")
    print(f"当前值：{translator.config.api_base_url}")
    new_value = validate_input("请输入新的API Base URL（例如 https://api.openai.com，输入'返回'以取消）：")
    if new_value != "返回":
        translator.config.api_base_url = new_value
        save_config(translator.config)
        logger.info(f"API Base URL已更新为：{new_value}")
    else:
        print("操作取消，返回设置菜单。")

def modify_api_endpoint(translator: Translator) -> None:
    """修改 API Endpoint"""
    print("\n设置 > 修改API Endpoint：")
    print(f"当前值：{translator.config.api_endpoint}")
    new_value = validate_input("请输入新的API Endpoint（例如 /v1/chat/completions，输入'返回'以取消）：")
    if new_value != "返回":
        translator.config.api_endpoint = new_value
        save_config(translator.config)
        logger.info(f"API Endpoint已更新为：{new_value}")
    else:
        print("操作取消，返回设置菜单。")

def modify_temperature(translator: Translator) -> None:
    """修改模型温度"""
    modify_setting(
        translator=translator,
        setting_name="temperature",
        prompt="模型温度",
        type_cast=float,
        range_check=(0, 2)
    )

def modify_top_p(translator: Translator) -> None:
    """修改 Top-P 值"""
    modify_setting(
        translator=translator,
        setting_name="top_p",
        prompt="Top-P核采样",
        type_cast=float,
        range_check=(0, 1)
    )

def modify_translation_mode(translator: Translator) -> None:
    """修改默认翻译模式"""
    print("\n设置 > 修改默认翻译模式：")
    mode_map = {k: v["desc"] for k, v in translator.mode_menu_items.items() if k not in [0, "00"]}
    print(f"当前值：{mode_map.get(translator.config.translation_mode, '未知')}")
    valid_modes = [k for k in translator.mode_menu_items.keys() if k not in [0, "00"]]
    new_value = validate_input(f"请输入新的默认翻译模式（{min(valid_modes)}-{max(valid_modes)}，输入'返回'以取消）：", valid_options=valid_modes, type_cast=int)
    if new_value != "返回":
        translator.config.translation_mode = new_value
        save_config(translator.config)
        logger.info(f"默认翻译模式已更新为：{mode_map[new_value]}")
    else:
        print("操作取消，返回设置菜单。")

def modify_max_text_length(translator: Translator) -> None:
    """修改最大翻译文本字数"""
    modify_setting(
        translator=translator,
        setting_name="max_text_length",
        prompt="最大翻译文本字数",
        type_cast=int,
        range_check=(1, float('inf'))
    )

def modify_context_max_count(translator: Translator) -> None:
    """修改最大上下文数量"""
    modify_setting(
        translator=translator,
        setting_name="context_max_count",
        prompt="最大上下文数量",
        type_cast=int,
        range_check=(0, 20)
    )



def toggle_debug_mode(translator: Translator) -> None:
    """切换调试模式"""
    print("\n设置 > 开启/关闭调试模式：")
    print(f"当前值：{'开启' if translator.config.debug_mode else '关闭'}")
    new_value = validate_input("请输入'开启'或'关闭'（输入'返回'以取消）：", valid_options=["开启", "关闭"])
    if new_value == "开启":
        translator.config.debug_mode = True
        logger.setLevel(logging.DEBUG)
        console_handler.setLevel(logging.DEBUG)
        file_handler.setLevel(logging.DEBUG)
        save_config(translator.config)
        logger.info("调试模式已开启")
    elif new_value == "关闭":
        translator.config.debug_mode = False
        logger.setLevel(logging.INFO)
        console_handler.setLevel(logging.INFO)
        file_handler.setLevel(logging.INFO)
        save_config(translator.config)
        logger.info("调试模式已关闭")
    else:
        print("操作取消，返回设置菜单。")

def modify_request_min_interval(translator: Translator) -> None:
    """修改请求最小间隔"""
    modify_setting(
        translator=translator,
        setting_name="request_min_interval",
        prompt="请求最小间隔",
        type_cast=float,
        range_check=(0.5, 2.0),
        pre_display_func=lambda x: f"{x} 秒"
    )

def modify_log_max_entries(translator: Translator) -> None:
    """修改日志最大条目数"""
    print("\n设置 > 修改日志最大条目数：")
    print(f"当前值：INFO {translator.config.log_info_max}，其他 {translator.config.log_other_max}")
    info_max = validate_input("请输入新的 INFO 日志最大条目数（建议50-200，输入'返回'以取消）：", type_cast=int, range_check=(50, 200))
    if info_max == "返回":
        print("操作取消，返回设置菜单。")
        return
    other_max = validate_input("请输入新的非 INFO 日志最大条目数（建议10-50，输入'返回'以取消）：", type_cast=int, range_check=(10, 50))
    if other_max == "返回":
        print("操作取消，返回设置菜单。")
        return
    translator.config.log_info_max = info_max
    translator.config.log_other_max = other_max
    file_handler.info_max = info_max
    file_handler.other_max = other_max
    save_config(translator.config)
    logger.info(f"日志最大条目数已更新为：INFO {info_max}，其他 {other_max}")

def toggle_show_gui_progress(translator: Translator) -> None:
    """切换 GUI 等待提示"""
    print("\n设置 > 开启/关闭GUI等待提示：")
    print(f"当前值：{'开启' if translator.config.show_gui_progress else '关闭'}")
    new_value = validate_input("请输入'开启'或'关闭'（输入'返回'以取消）：", valid_options=["开启", "关闭"])
    if new_value == "开启":
        translator.config.show_gui_progress = True
        save_config(translator.config)
        logger.info("GUI等待提示已开启")
    elif new_value == "关闭":
        translator.config.show_gui_progress = False
        save_config(translator.config)
        logger.info("GUI等待提示已关闭")
    else:
        print("操作取消，返回设置菜单。")

def add_custom_translation_mode(translator: Translator) -> None:
    """添加自定义翻译模式"""
    print("\n设置 > 添加自定义翻译模式：")
    source_lang = validate_input("请输入源语言名称（如'中文'）：")
    if source_lang == "返回":
        print("操作取消，返回设置菜单。")
        return
    source_code = validate_input("请输入源语言代码（ISO 639-1，如'zh'）：")
    if source_code == "返回":
        print("操作取消，返回设置菜单。")
        return
    target_lang = validate_input("请输入目标语言名称（如'英文'）：")
    if target_lang == "返回":
        print("操作取消，返回设置菜单。")
        return
    target_code = validate_input("请输入目标语言代码（ISO 639-1，如'en'）：")
    if target_code == "返回":
        print("操作取消，返回设置菜单。")
        return
    style = validate_input("请输入语气风格（可选，如'敬语'，按回车跳过）：", allow_empty=True) or ""
    default_lang = validate_input("请输入默认语言名称（通常为源语言，如'中文'）：", allow_empty=True) or source_lang

    # 检查语言代码是否已存在于系统中
    is_new_source = source_code not in translator.mode_config["tone_particles"]
    is_new_target = target_code not in translator.mode_config["tone_particles"]
    
    # 询问新语言的语气词配置
    if is_new_source:
        source_tone_pattern = validate_input(f"请输入{source_lang}({source_code})的语气词正则表达式（可选，按回车使用默认值）：", allow_empty=True)
        if source_tone_pattern:
            translator.mode_config["tone_particles"][source_code] = source_tone_pattern
            logger.info(f"已添加{source_lang}({source_code})的自定义语气词: {source_tone_pattern}")
    
    if is_new_target:
        target_tone_pattern = validate_input(f"请输入{target_lang}({target_code})的语气词正则表达式（可选，按回车使用默认值）：", allow_empty=True)
        if target_tone_pattern:
            translator.mode_config["tone_particles"][target_code] = target_tone_pattern
            logger.info(f"已添加{target_lang}({target_code})的自定义语气词: {target_tone_pattern}")

    # 创建新的翻译模式
    new_mode_id = max([k for k in translator.mode_config["translation_modes"].keys() if isinstance(k, int)]) + 1
    translator.mode_config["translation_modes"][new_mode_id] = {
        "source_lang": source_lang,
        "target_lang": target_lang,
        "style": style,
        "default_lang": default_lang,
        "source_code": source_code,
        "target_code": target_code
    }

    translator.mode_menu_items[new_mode_id] = {"desc": f"{source_lang}-{target_lang}" + (f"-{style}" if style else "")}
    translator.history[new_mode_id] = []
    
    # 补全新增语言的特征和语气词
    translator.mode_config = complete_language_features_and_tones(translator.mode_config)
    save_mode_config(translator.mode_config)
    logger.info(f"已添加自定义翻译模式 {new_mode_id}: {source_lang} ({source_code}) -> {target_lang} ({target_code})，风格：{style or '无'}")

def delete_translation_mode(translator: Translator) -> None:
    """删除翻译模式"""
    print("\n设置 > 删除翻译模式：")
    if len([k for k in translator.mode_config["translation_modes"].keys() if isinstance(k, int)]) <= 1:
        logger.warning("至少保留一个翻译模式，无法删除")
        return

    mode_map = {k: v["desc"] for k, v in translator.mode_menu_items.items() if k not in [0, "00"]}
    for key, desc in mode_map.items():
        print(f"  {key}. {desc}")

    valid_modes = [k for k in mode_map.keys()]
    mode_to_delete = validate_input(f"请输入要删除的模式编号（{min(valid_modes)}-{max(valid_modes)}，输入'返回'以取消）：", valid_options=valid_modes, type_cast=int)
    if mode_to_delete == "返回":
        print("操作取消，返回设置菜单。")
        return

    if mode_to_delete == translator.config.translation_mode:
        logger.warning("无法删除当前默认翻译模式，请先切换默认模式")
        return

    del translator.mode_config["translation_modes"][mode_to_delete]
    del translator.mode_menu_items[mode_to_delete]
    if mode_to_delete in translator.history:
        del translator.history[mode_to_delete]
    save_mode_config(translator.mode_config)
    logger.info(f"已删除翻译模式 {mode_to_delete}: {mode_map[mode_to_delete]}")

def enter_settings_menu(translator: Translator) -> None:
    """进入设置菜单"""
    while True:
        display_settings_menu(SETTINGS_MENU_ITEMS, "设置", translator.config, translator)
        choice = input().strip()
        if choice.lower() == "返回":
            break
        try:
            choice_int = int(choice)
            if choice_int not in SETTINGS_MENU_ITEMS:
                print("无效选项，请输入菜单中的数字或'返回'返回主菜单。")
                continue
            SETTINGS_MENU_ITEMS[choice_int]["func"](translator)
        except ValueError:
            print("请输入有效的数字选项，或输入'返回'返回主菜单。")

gui_handler = None

def main():
    """主函数"""
    # 检查cryptography库
    try:
        import cryptography
        logger.debug(f"已加载cryptography库版本: {cryptography.__version__}")
    except ImportError:
        print("错误: 未找到cryptography库，请先安装:")
        print("pip install cryptography")
        sys.exit(1)
    
    # 加载配置
    config = Config(**load_config())

    # 创建tkinter根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建GUI处理器和翻译器
    global gui_handler
    gui_handler = GUIHandler(root)
    translator = Translator(config, root)
    
    # 创建键盘监听器并设置为守护线程
    keyboard_listener = KeyboardListener(translator)
    keyboard_listener.start()
    
    # 全局退出标志
    global exiting
    exiting = False
    
    # 处理优雅退出
    def graceful_shutdown():
        """确保程序优雅退出，关闭所有资源"""
        global exiting
        if exiting:
            return
        exiting = True
        
        logger.info("正在关闭翻译程序...")
        try:
            # 关闭键盘监听器
            if keyboard_listener and keyboard_listener.listener and keyboard_listener.listener.running:
                keyboard_listener.listener.stop()
                
            # 关闭翻译器
            if translator:
                translator.shutdown()
            
            # 关闭GUI
            if gui_handler and gui_handler.is_showing:
                # 如果GUI正在显示，确保它被正确关闭
                root.after(0, gui_handler._cleanup_resources)
                
            # 关闭日志处理器
            for handler in logger.handlers[:]:
                try:
                    handler.close()
                    logger.removeHandler(handler)
                except Exception as e:
                    print(f"关闭日志处理器时出错: {e}", file=sys.stderr)
            
            # 手动触发垃圾回收
            logger.debug("执行垃圾回收...")
            gc.collect()

            # 确保程序完全退出
            root.destroy()
            logger.debug("资源释放完毕，程序退出")
            # 强制退出以防其他线程阻止退出
            os._exit(0)
        except Exception as e:
            print(f"关闭时发生错误: {e}", file=sys.stderr)
            os._exit(1)
    
    # 注册退出处理
    root.protocol("WM_DELETE_WINDOW", graceful_shutdown)
    
    # 设置信号处理器用于命令行中断
    try:
        import signal
        def signal_handler(sig, frame):
            logger.info(f"接收到信号 {sig}，准备优雅退出")
            graceful_shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    except (ImportError, AttributeError) as e:
        logger.warning(f"无法设置信号处理器: {e}")
    except Exception as e:
        logger.warning(f"设置信号处理器时出现未知错误: {e}")

    def run_console():
        """运行控制台界面"""
        try:
            logger.info("控制台线程已启动，准备进入循环。") # 新增日志
            while not exiting:
                logger.info("控制台循环开始，准备显示菜单。") # 新增日志
                display_menu(translator.mode_menu_items, "翻译模式选择", config.translation_mode)
                logger.info("菜单已显示，等待用户输入。") # 新增日志
                try:
                    choice = input("请选择翻译模式（输入数字切换模式，0进入设置，00清空上下文和翻译缓存，q退出）：").strip()
                    logger.info(f"用户输入: {choice}") # 新增日志
                    if choice.lower() in ['q', 'quit', 'exit']:
                        logger.info("用户请求退出程序")
                        graceful_shutdown()
                        break
                        
                    if choice == "0":
                        enter_settings_menu(translator)
                    elif choice == "00":
                        translator.clear_all_context()
                        print("已清空所有上下文和翻译缓存")
                    else:
                        choice_int = int(choice)
                        valid_modes = [k for k in translator.mode_menu_items.keys() if k not in [0, "00"]]
                        if choice_int not in valid_modes:
                            print(f"无效模式，请输入 {min(valid_modes)}-{max(valid_modes)} 之间的数字，或 0/00。")
                            continue
                        config.translation_mode = choice_int
                        save_config(config)
                        logger.info(f"已切换到翻译模式 {choice_int}: {translator.mode_menu_items[choice_int]['desc']}")
                except ValueError:
                    print("请输入有效的数字（如 1, 0, 00），或检查输入是否有误。")
                except EOFError:
                    logger.info("检测到EOF，退出控制台")
                    graceful_shutdown()
                    break
                time.sleep(0.1)
                # 在控制台循环的末尾尝试垃圾回收
                gc.collect()
        except Exception as e:
            logger.error(f"控制台线程异常: {e}")
            graceful_shutdown()

    # 启动控制台线程
    console_thread = threading.Thread(target=run_console, daemon=True)
    console_thread.start()

    # 检查网络和API状态并显示初始化消息
    is_network_ok = translator.service_manager.is_network_connected()
    if is_network_ok:
        # 异步检查API健康状态
        asyncio.run_coroutine_threadsafe(translator.check_api_health(), translator.loop)
        
        # 如果已经获取到API健康状态，显示相应消息
        if translator.api_health_status["healthy"] is not None:
            if translator.api_health_status["healthy"]:
                status_msg = "API服务正常"
            else:
                status_msg = f"API服务异常: {translator.api_health_status['message']}"
            logger.info(status_msg)
    else:
        logger.warning("网络连接不可用，可能会影响翻译功能")

    logger.info("翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式")
    
    try:
        root.mainloop()
    except Exception as e:
        logger.error(f"主循环异常: {e}")
    finally:
        graceful_shutdown()

# 添加一个辅助函数来检测操作系统，在提示文本中可能需要区分不同操作系统的快捷键
def get_os_type():
    """获取操作系统类型"""
    if sys.platform.startswith('win'):
        return 'windows'
    elif sys.platform.startswith('darwin'):
        return 'macos'
    elif sys.platform.startswith('linux'):
        return 'linux'
    else:
        return 'unknown'

def modify_setting(translator: Translator, setting_name: str, 
                  prompt: str, validation_func: Callable = None,
                  type_cast: Callable = str, range_check: tuple = None,
                  allow_empty: bool = False, valid_options: list = None,
                  transform_func: Callable = None,
                  pre_display_func: Callable = None) -> None:
    """通用设置修改函数，替代多个单独的modify_X函数
    
    Args:
        translator: 翻译器实例
        setting_name: 配置中的设置名称
        prompt: 显示给用户的提示文本
        validation_func: 自定义验证函数，返回(bool, str)表示(是否有效, 错误消息)
        type_cast: 类型转换函数
        range_check: 数值范围检查(min, max)
        allow_empty: 是否允许空输入
        valid_options: 有效的输入选项列表
        transform_func: 输入值转换函数，在保存前处理输入值
        pre_display_func: 显示当前值的自定义处理函数
    """
    config = translator.config
    current_value = getattr(config, setting_name)
    
    print(f"\n设置 > {prompt}：")
    
    # 显示当前值，可能应用自定义显示处理
    if pre_display_func:
        display_value = pre_display_func(current_value)
    else:
        display_value = current_value
    print(f"当前值：{display_value}")
    
    # 如果未提供valid_options，使用空列表
    valid_options = valid_options or []
    
    new_value = validate_input(
        f"请输入新的{prompt}，输入'返回'以取消: ",
        valid_options=valid_options, 
        type_cast=type_cast, 
        range_check=range_check, 
        allow_empty=allow_empty
    )
    
    if new_value != "返回":
        # 应用自定义验证
        if validation_func:
            is_valid, error_msg = validation_func(new_value)
            if not is_valid:
                print(error_msg)
                return
        
        # 应用变换函数
        if transform_func:
            new_value = transform_func(new_value)
            
        # 设置新值，如果新值与当前值不同并且不是空输入
        if new_value != "" and new_value != current_value:
            setattr(config, setting_name, new_value)
            save_config(config)
            logger.info(f"{prompt}已更新为: {new_value}")
        else:
            print(f"{prompt}保持不变")
    else:
        print("操作取消，返回设置菜单。")

def modify_language_families(translator: Translator) -> None:
    """修改语言族配置"""
    print("\n设置 > 修改语言族配置：")
    
    # 显示当前语言族配置
    current_families = translator.config.language_families or {}
    print("当前语言族配置：")
    for family, langs in current_families.items():
        print(f"  {family}: {', '.join(langs)}")
    
    # 操作选项
    print("\n操作选项：")
    print("  1. 添加新的语言族")
    print("  2. 修改现有语言族")
    print("  3. 删除语言族")
    
    action = validate_input("请选择操作（输入'返回'以取消）：", valid_options=[1, 2, 3], type_cast=int)
    if action == "返回":
        print("操作取消，返回设置菜单。")
        return
    
    # 添加新的语言族
    if action == 1:
        family_name = validate_input("请输入新语言族名称（如'cjk'，'european'等）：")
        if family_name == "返回":
            print("操作取消，返回设置菜单。")
            return
            
        if family_name in current_families:
            print(f"语言族 '{family_name}' 已存在，请选择其他名称或使用修改选项。")
            return
            
        langs_input = validate_input("请输入该语言族包含的语言代码，以逗号分隔（如'zh,ja,ko'）：")
        if langs_input == "返回":
            print("操作取消，返回设置菜单。")
            return
        
        langs = [lang.strip() for lang in langs_input.split(",") if lang.strip()]
        if not langs:
            print("语言列表不能为空，操作取消。")
            return
            
        # 更新配置
        new_families = dict(current_families)
        new_families[family_name] = langs
        translator.config.language_families = new_families
        save_config(translator.config)
        logger.info(f"已添加新语言族 '{family_name}': {langs}")
    
    # 修改现有语言族
    elif action == 2:
        if not current_families:
            print("当前没有语言族配置，请先添加。")
            return
            
        family_names = list(current_families.keys())
        print("现有语言族：")
        for i, name in enumerate(family_names, 1):
            print(f"  {i}. {name}: {', '.join(current_families[name])}")
        
        family_index = validate_input("请选择要修改的语言族编号：", 
                                      valid_options=list(range(1, len(family_names)+1)), 
                                      type_cast=int)
        if family_index == "返回":
            print("操作取消，返回设置菜单。")
            return
        
        family_name = family_names[family_index-1]
        current_langs = current_families[family_name]
        
        print(f"当前 '{family_name}' 包含语言: {', '.join(current_langs)}")
        langs_input = validate_input(f"请输入修改后的语言代码列表，以逗号分隔：")
        if langs_input == "返回":
            print("操作取消，返回设置菜单。")
            return
        
        langs = [lang.strip() for lang in langs_input.split(",") if lang.strip()]
        if not langs:
            print("语言列表不能为空，操作取消。")
            return
            
        # 更新配置
        new_families = dict(current_families)
        new_families[family_name] = langs
        translator.config.language_families = new_families
        save_config(translator.config)
        logger.info(f"已更新语言族 '{family_name}': {langs}")
    
    # 删除语言族
    elif action == 3:
        if not current_families:
            print("当前没有语言族配置，无需删除。")
            return
            
        family_names = list(current_families.keys())
        print("现有语言族：")
        for i, name in enumerate(family_names, 1):
            print(f"  {i}. {name}: {', '.join(current_families[name])}")
        
        family_index = validate_input("请选择要删除的语言族编号：", 
                                      valid_options=list(range(1, len(family_names)+1)), 
                                      type_cast=int)
        if family_index == "返回":
            print("操作取消，返回设置菜单。")
            return
        
        family_name = family_names[family_index-1]
        confirm = validate_input(f"确定要删除语言族 '{family_name}'? (是/否)：", valid_options=["是", "否"])
        if confirm != "是":
            print("操作取消，返回设置菜单。")
            return
            
        # 更新配置
        new_families = dict(current_families)
        del new_families[family_name]
        translator.config.language_families = new_families
        save_config(translator.config)
        logger.info(f"已删除语言族 '{family_name}'")

def modify_language_cache_size(translator: Translator) -> None:
    """修改语言检测缓存大小"""
    modify_setting(
        translator=translator,
        setting_name="language_detection_cache_size",
        prompt="语言检测缓存大小",
        type_cast=int,
        range_check=(1, 1000),
        pre_display_func=lambda x: f"{x} 条"
    )
    # 如果修改了缓存大小，重新初始化缓存
    global language_detection_cache
    if language_detection_cache is not None:
        old_size = len(language_detection_cache)
        new_size = translator.config.language_detection_cache_size
        language_detection_cache = LRUCache(new_size)
        logger.info(f"已重置语言检测缓存，大小从 {old_size} 更改为 {new_size}")

def modify_same_language_threshold(translator: Translator) -> None:
    """修改翻译结果相似度阈值"""
    modify_setting(
        translator=translator,
        setting_name="same_language_match_threshold",
        prompt="翻译结果相似度阈值",
        type_cast=float,
        range_check=(0, 1),
        pre_display_func=lambda x: f"{x:.2f}"
    )

def modify_thinking_budget(translator: Translator) -> None:
    """修改思考预算 (Tokens)"""
    current_budget = getattr(translator.config, 'thinking_budget_tokens', 0)
    print("\n设置 > 修改思考预算 (Tokens)：")
    print(f"当前值：{current_budget} Tokens {'（思考模式已关闭）' if current_budget == 0 else ''}")
    
    if "gemini-2.5" in translator.config.model_id:
        print("\n提示：您正在使用Gemini 2.5模型，支持思考模式。")
        print("- 输入 0 表示关闭思考模式")
        print("- 推荐范围：1024-8192 tokens")
        print("- 最大支持：24576 tokens")
        print("- 数值越大，推理能力越强，但会增加API调用成本和延迟")
    else:
        print(f"\n提示：您当前使用的模型 ({translator.config.model_id}) 不支持思考模式。")
        print("- 如需使用思考功能，请先切换到Gemini 2.5系列模型")
        print("- 虽然可以设置思考预算，但在非支持模型上不会生效")
    
    new_value = validate_input("\n请输入新的思考预算 (Tokens)（输入'返回'以取消）：", type_cast=int, range_check=(0, 24576))
    if new_value != "返回":
        translator.config.thinking_budget_tokens = new_value
        save_config(translator.config)
        
        if new_value == 0:
            logger.info(f"思考预算已更新为：{new_value} Tokens（思考模式已关闭）")
        else:
            if "gemini-2.5" in translator.config.model_id:
                logger.info(f"思考预算已更新为：{new_value} Tokens（思考模式已启用）")
            else:
                logger.info(f"思考预算已更新为：{new_value} Tokens（注意：当前模型不支持思考模式）")
    else:
        print("操作取消，返回设置菜单。")

# 缓存管理设置菜单函数
def modify_cache_settings(translator: Translator) -> None:
    """修改缓存管理设置"""
    config = translator.config
    cache_settings_menu = {
        1: {"desc": "开启/关闭本地缓存", "func": lambda t: toggle_local_cache(t)},
        2: {"desc": "设置缓存优先级", "func": lambda t: toggle_cache_priority(t)},
        3: {"desc": "修改本地缓存路径", "func": lambda t: modify_local_cache_path(t)},
        4: {"desc": "修改最大缓存条目数", "func": lambda t: modify_cache_max_entries(t)},
        5: {"desc": "修改缓存写入延迟", "func": lambda t: modify_cache_write_delay(t)},
        6: {"desc": "立即保存所有缓存", "func": lambda t: save_all_cache(t)},
        7: {"desc": "清空所有缓存", "func": lambda t: clear_all_cache(t)}
    }
    
    while True:
        print("\n缓存管理设置：")
        for key, item in cache_settings_menu.items():
            extra_info = ""
            if key == 1:
                extra_info = f"（当前：{'开启' if config.use_local_cache else '关闭'}）"
            elif key == 2:
                extra_info = f"（当前：{'优先使用缓存' if config.cache_priority else '优先使用大模型'}）"
            elif key == 3:
                extra_info = f"（当前：{config.local_cache_path}）"
            elif key == 4:
                extra_info = f"（当前：{config.cache_max_entries}）"
            elif key == 5:
                extra_info = f"（当前：{config.cache_write_delay} 秒）"
            print(f"  {key}. {item['desc']} {extra_info}")
        print("请选择（输入数字或'返回'返回上级菜单）：")
        
        choice = input().strip()
        if choice.lower() == "返回":
            break
        try:
            choice_int = int(choice)
            if choice_int not in cache_settings_menu:
                print("无效选项，请输入菜单中的数字或'返回'返回上级菜单。")
                continue
            cache_settings_menu[choice_int]["func"](translator)
        except ValueError:
            print("请输入有效的数字选项，或输入'返回'返回上级菜单。")

def toggle_local_cache(translator: Translator) -> None:
    """开启/关闭本地缓存"""
    current = translator.config.use_local_cache
    translator.config.use_local_cache = not current
    save_config(translator.config)
    
    status = "开启" if translator.config.use_local_cache else "关闭"
    logger.info(f"本地缓存已{status}")
    print(f"本地缓存已{status}")
    
    # 如果开启缓存，检查缓存管理器是否存在
    if translator.config.use_local_cache and not hasattr(translator, 'cache_manager'):
        from cache_manager import CacheManager
        translator.cache_manager = CacheManager(translator.config)
        logger.info(f"已重新初始化缓存管理器，数据库路径: {translator.config.local_cache_path}")

def toggle_cache_priority(translator: Translator) -> None:
    """切换缓存优先级"""
    current = translator.config.cache_priority
    translator.config.cache_priority = not current
    save_config(translator.config)
    
    status = "优先使用缓存" if translator.config.cache_priority else "优先使用大模型"
    logger.info(f"缓存优先级已设置为: {status}")
    print(f"缓存优先级已设置为: {status}")

def modify_local_cache_path(translator: Translator) -> None:
    """修改本地缓存路径"""
    print("\n设置 > 缓存管理 > 修改本地缓存路径：")
    print(f"当前值：{translator.config.local_cache_path}")
    new_value = validate_input("请输入新的本地缓存路径（输入'返回'以取消）：")
    
    if new_value != "返回":
        # 如果存在缓存管理器，先保存并关闭
        if hasattr(translator, 'cache_manager'):
            translator.cache_manager.save_pending_changes()
            translator.cache_manager.close()
        
        # 更新配置
        translator.config.local_cache_path = new_value
        save_config(translator.config)
        
        # 重新初始化缓存管理器
        from cache_manager import CacheManager
        translator.cache_manager = CacheManager(translator.config)
        
        logger.info(f"本地缓存路径已更新为：{new_value}")
    else:
        print("操作取消，返回缓存设置菜单。")

def modify_cache_max_entries(translator: Translator) -> None:
    """修改最大缓存条目数"""
    modify_setting(
        translator=translator,
        setting_name="cache_max_entries",
        prompt="请输入最大缓存条目数（建议100-10000）：",
        type_cast=int,
        range_check=(10, 100000)
    )
    
    # 如果缓存管理器存在，主动执行一次清理
    if hasattr(translator, 'cache_manager'):
        translator.cache_manager._cleanup_memory_cache()
        translator.cache_manager.cleanup_local_cache()
        logger.info("已根据新设置执行缓存清理")

def modify_cache_write_delay(translator: Translator) -> None:
    """修改缓存写入延迟"""
    modify_setting(
        translator=translator,
        setting_name="cache_write_delay",
        prompt="请输入缓存写入延迟秒数（建议0.5-10）：",
        type_cast=float,
        range_check=(0.1, 60.0)
    )

def save_all_cache(translator: Translator) -> None:
    """立即保存所有缓存"""
    if hasattr(translator, 'cache_manager'):
        print("正在保存所有未写入的缓存...")
        translator.cache_manager.save_pending_changes()
        print("缓存保存完成")
        logger.info("已手动保存所有缓存")
    else:
        print("缓存管理器未初始化，无法保存")

def clear_all_cache(translator: Translator) -> None:
    """清空所有缓存（内存和本地）"""
    confirm = input("确定要清空所有缓存（包括内存和本地数据库）吗？此操作不可撤销。(y/n): ").strip().lower()
    if confirm == 'y':
        # 清空内存缓存
        if hasattr(translator, 'translation_cache'):
            translator.translation_cache.clear()
            print("内存缓存已清空")
            
        # 清空本地缓存
        if hasattr(translator, 'cache_manager'):
            translator.cache_manager.clear_all_cache()
            print("本地缓存已清空")
            
        logger.info("用户已手动清空所有缓存")
    else:
        print("操作已取消")

if __name__ == "__main__":
    main()