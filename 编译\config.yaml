﻿# 翻译程序主配置文件
# 版本：2.0.7

# API 配置
api_key: ""  # 必须使用加密格式的API密钥，使用api_crypto.py工具加密
model_id: "gemini-2.5-flash-preview-04-17"  # 使用的主模型ID
gemini_fallback_model_id: "gemini-2.0-flash-lite-preview-02-05" # Gemini API 备用模型ID
openai_fallback_model_id: "gpt-4o"  # OpenAI API 备用模型ID
api_mode: "gemini"  # API 模式，可选 "gemini"（谷歌API） 或 "openai"（OpenAI兼容API）
api_base_url: "https://api.openai.com"  # OpenAI兼容API的基础URL，仅在 api_mode 为 "openai" 时生效
api_endpoint: "/v1/chat/completions"  # OpenAI兼容API的端点，仅在 api_mode 为 "openai" 时生效

# 模型生成参数
temperature: 0.1  # 模型温度，控制生成文本的随机性，范围 0-2，建议 0-1，默认 0.1
top_p: 0.85  # Top-P 核采样值，控制生成文本的多样性，范围 0-1，建议 0.5-1，默认 0.85
max_output_tokens: 2048  # 生成的最大输出标记数，范围 1-4096，默认 1024
top_k: 64  # Top-K采样，范围 1-100，默认 30
frequency_penalty: 0.0 # 频率惩罚，范围 0-2，默认 0.0
presence_penalty: 0.0 # 存在惩罚，范围 0-2，默认 0.0

# 翻译行为配置
translation_mode: 2  # 默认翻译模式编号，对应 mode_config.yaml 中的模式，建议 1-5
max_text_length: 500  # 最大翻译文本长度（字符数），超过此长度将拒绝翻译，建议 100-1000
context_max_count: 8  # 上下文最大数量，用于保持翻译一致性，范围 0-20，默认 8
short_text_threshold: 10  # 短文本阈值，小于此长度的文本使用优化检测逻辑，建议 5-20
lang_detection_threshold: 0.9  # 语言检测置信度阈值，低于此值将使用特征检查，范围 0-1，默认 0.9

# 网络和请求配置
# TCP连接设置
tcp_connector:
  limit: 10  # 限制同时连接数 
  ttl_dns_cache: 300  # DNS缓存时间（秒）
  keepalive_timeout: 60  # 保持连接活跃时间（秒）

# 超时设置（秒）
timeout:
  total: 30  # 总超时时间
  connect: 10 # 连接超时
  sock_connect: 10  # 套接字连接超时
  sock_read: 20  # 套接字读取超时

# 网络检查设置
network_check:
  hosts:
    - *******
    - *******
  port: 53  # 网络检查端口
  timeout: 1  # 网络检查超时（秒） - 将这里改为1秒

# API健康检查设置
api_health_check:
  timeout_total: 10  # 总超时（秒）
  timeout_connect: 5  # 连接超时（秒）
  timeout_sock_connect: 5  # 套接字连接超时（秒）
  timeout_sock_read: 8  # 套接字读取超时（秒）
  test_prompt: "Hello, API check"  # 测试提示词

# 网络配置
request_min_interval: 1.0  # 两次翻译请求之间的最小间隔（秒），防止过于频繁请求，建议 0.5-2，默认 1.0

# 日志和调试
debug_mode: false   # 调试模式，true 为开启（显示详细日志），false 为关闭，默认 false
log_info_max: 50  # 日志文件中 INFO 级别的最大条目数，建议 50-200，默认 50
log_other_max: 50  # 日志文件中非 INFO 级别的最大条目数（WARNING/ERROR），建议 10-50，默认 50

# GUI 配置
show_gui_progress: true  # 是否显示GUI等待提示，true为显示，false为不显示

# 文本过滤配置
common_symbols: '[,.!?;:"\''\''()[\]\{\}<>+=*/&@#$%^&*~|_，。！？；：、""\''\''（）【】《》]' # 通用的常用符号集，正则表达式，翻译中保留这些字符
illegal_chars: '[\x00-\x1F\x7F-\x9F]'    # 需要移除的非法字符，正则表达式，例如控制字符

# 安全设置
safety_settings:
  gemini:
    - category: HARM_CATEGORY_HARASSMENT
      threshold: BLOCK_NONE
    - category: HARM_CATEGORY_HATE_SPEECH
      threshold: BLOCK_NONE
    - category: HARM_CATEGORY_SEXUALLY_EXPLICIT
      threshold: BLOCK_NONE
    - category: HARM_CATEGORY_DANGEROUS_CONTENT
      threshold: BLOCK_NONE
language_detection_cache_size: 100  # 语言检测缓存大小
translation_cache_size: 50  # 翻译结果缓存大小
same_language_match_threshold: 0.5  # 检测翻译结果与原文相似度的阈值，范围0-1，越高检测越严格，默认0.5

# 语言检测和消歧配置
language_detection:
  ambiguity_factor: 1.4  # 语言检测歧义判断系数，范围1.0-2.0，越大越容易区分语言
  hint_bias: 0.2  # 语言提示偏置值，范围0.0-0.5，越大越信任提示语言
  prob_weight: 0.7  # 主检测器权重，范围0.0-1.0
  feature_weight: 0.3  # 特征匹配权重，范围0.0-1.0
  short_text_prob_weight: 0.4  # 短文本主检测器权重，范围0.0-1.0
  short_text_feature_weight: 0.6  # 短文本特征匹配权重，范围0.0-1.0
  min_char_threshold: 10  # 最小字符检查阈值
# 字符比例配置
translation_quality:
  min_char_ratio: 0.2  # 默认最小字符比例阈值，翻译结果字符数不应少于原文的这一比例
  default_feature_dominance_ratio: 2.0  # 默认特征主导比例，用于语言对消歧

# 语言家族分组
language_families:
  cjk:  # 中日韩语系
    - zh
    - ja
    - ko
  european:  # 欧洲语系
    - en
    - fr
    - de
    - es
    - it
    - pt
    - ru
  indic:  # 印度语系
    - hi
    - bn
    - ur
  southeast_asian:  # 东南亚语系
    - th
    - vi
    - id
    - ms
    - km
    - lo
  semitic:  # 闪米特语系
    - ar
    - he
language_specific_settings: {}  # 每种语言的特定设置

# 通用语气符号配置
universal_punctuation:
  question_marks:
    universal:  # 所有语言通用的问号
      - '?'
      - ？
    latin:  # 拉丁语系
      - '?'
    cjk:  # 中日韩
      - ？
  exclamation_marks:
    universal:
      - '!'
      - ！
    latin:
      - '!'
    cjk:
      - ！
thinking_budget_tokens: 0
ko_zh_detection:
  enabled: true
  ko_specific_ratio_threshold: 0.3
  lang_feature_score_threshold: 0.3
  feature_dominance_ratio: 2.0
  cjk_feature_score_threshold: 0.35
